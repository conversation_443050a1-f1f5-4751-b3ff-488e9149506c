# Yasmin Al-Sham Supabase Migration Guide
# دليل ترحيل ياسمين الشام إلى Supabase

## Overview / نظرة عامة

This guide documents the migration from localStorage-based data storage to a fully integrated Supabase database system for the Yasmin Al-Sham project.

يوثق هذا الدليل عملية الترحيل من تخزين البيانات المحلي إلى نظام قاعدة بيانات Supabase المتكامل لمشروع ياسمين الشام.

## Migration Components / مكونات الترحيل

### 1. Enhanced Stores / المتاجر المحسنة

#### Authentication Store v2 (`src/store/authStore-v2.ts`)
- **Features**: Supabase Auth integration, fallback to mock data
- **Migration**: Automatic user profile sync with database
- **Fallback**: Mock users for development when Supabase unavailable

#### Data Store v2 (`src/store/dataStore-v2.ts`)
- **Features**: Orders, workers, and appointments management
- **Migration**: Full CRUD operations with Supabase
- **Fallback**: In-memory storage for development

#### Shop Store v2 (`src/store/shopStore-v2.ts`)
- **Features**: Cart and favorites with user-specific data
- **Migration**: User-based cart/favorites in database
- **Fallback**: localStorage for guest users

### 2. Migration Utilities / أدوات الترحيل

#### Migration Utility (`src/lib/migration-utility.ts`)
- **Purpose**: Automated data migration from old stores
- **Features**: 
  - Auth data migration
  - Orders data migration
  - Workers data migration
  - Shop data migration
  - Error handling and status tracking

#### Migration Manager Component (`src/components/MigrationManager.tsx`)
- **Purpose**: UI for managing migration process
- **Features**:
  - Progress tracking
  - Error/warning display
  - Manual migration trigger
  - Old data cleanup

## Migration Process / عملية الترحيل

### Step 1: Pre-Migration Check
```typescript
import { isMigrationNeeded } from '@/lib/migration-utility'

if (isMigrationNeeded()) {
  // Show migration UI or run automatic migration
}
```

### Step 2: Run Migration
```typescript
import { runMigration } from '@/lib/migration-utility'

const status = await runMigration()
console.log('Migration status:', status)
```

### Step 3: Update Component Imports
Replace old store imports with new ones:

```typescript
// Old
import { useAuthStore } from '@/store/authStore'
import { useDataStore } from '@/store/dataStore'
import { useShopStore } from '@/store/shopStore'

// New
import { useAuthStore } from '@/store/authStore-v2'
import { useDataStore } from '@/store/dataStore-v2'
import { useShopStore } from '@/store/shopStore-v2'
```

## Database Schema / مخطط قاعدة البيانات

The migration uses the existing comprehensive schema in `supabase-schema-v2.sql`:

### Core Tables / الجداول الأساسية
- `users` - User profiles and authentication
- `workers` - Worker information and specialties
- `orders` - Order management and tracking
- `appointments` - Appointment scheduling
- `favorites` - User favorites
- `cart_items` - Shopping cart items

### RLS Policies / سياسات الأمان
- **Admin**: Full access to all data
- **Worker**: Access to assigned orders and own data
- **Client**: Access to own data only
- **Anonymous**: Can create appointments only

## Usage Examples / أمثلة الاستخدام

### Authentication / المصادقة
```typescript
import { useAuthStore } from '@/store/authStore-v2'

const { signIn, signOut, user, isAuthenticated } = useAuthStore()

// Sign in
const success = await signIn('<EMAIL>', 'admin123')

// Check authentication
if (isAuthenticated()) {
  console.log('User:', user)
}
```

### Data Management / إدارة البيانات
```typescript
import { useDataStore } from '@/store/dataStore-v2'

const { loadOrders, addOrder, updateOrder } = useDataStore()

// Load orders
await loadOrders({ status: 'pending' })

// Add new order
const orderId = await addOrder({
  client_id: 'user-id',
  design_id: 'design-id',
  status: 'pending',
  total_price: 500
})
```

### Shop Management / إدارة المتجر
```typescript
import { useShopStore } from '@/store/shopStore-v2'

const { addToCart, addToFavorites, loadCart } = useShopStore()

// Add to cart (requires user ID for database storage)
await addToCart(product, 1, 'M', 'blue', userId)

// Load user's cart
await loadCart(userId)
```

## Fallback Behavior / السلوك الاحتياطي

When Supabase is not configured or unavailable:

### Authentication
- Uses mock users from `getFallbackUsers()`
- Default admin: `<EMAIL>` / `admin123`
- Default worker: `<EMAIL>` / `worker123`

### Data Storage
- Orders and workers stored in memory
- Data persists only during session
- No database operations performed

### Shop Data
- Cart and favorites stored in localStorage
- Data persists across sessions
- No user-specific data separation

## Migration Status Tracking / تتبع حالة الترحيل

The migration utility provides detailed status tracking:

```typescript
interface MigrationStatus {
  isComplete: boolean
  authMigrated: boolean
  ordersMigrated: boolean
  workersMigrated: boolean
  shopMigrated: boolean
  errors: string[]
  warnings: string[]
}
```

## Error Handling / معالجة الأخطاء

### Common Issues / المشاكل الشائعة

1. **Supabase Not Configured**
   - Check environment variables
   - Verify Supabase project settings
   - Fallback mode will be used

2. **Migration Failures**
   - Check network connectivity
   - Verify database permissions
   - Review error messages in migration status

3. **Data Conflicts**
   - Existing data in database may prevent migration
   - Check for duplicate IDs or constraint violations

### Error Recovery / استرداد الأخطاء

```typescript
import { storeMigration } from '@/lib/migration-utility'

// Get detailed migration status
const status = storeMigration.getStatus()

// Retry specific migration steps
if (!status.authMigrated) {
  await storeMigration.migrateAuthData()
}
```

## Testing / الاختبار

### Manual Testing Steps / خطوات الاختبار اليدوي

1. **Pre-Migration**
   - Verify old data exists in localStorage
   - Check Supabase configuration
   - Test fallback behavior

2. **During Migration**
   - Monitor migration progress
   - Check for errors/warnings
   - Verify data integrity

3. **Post-Migration**
   - Test all CRUD operations
   - Verify user authentication
   - Check data consistency

### Automated Testing / الاختبار الآلي

```typescript
// Test migration utility
import { storeMigration } from '@/lib/migration-utility'

describe('Store Migration', () => {
  test('should detect migration need', () => {
    expect(storeMigration.checkMigrationNeeded()).toBe(true)
  })
  
  test('should migrate auth data', async () => {
    const success = await storeMigration.migrateAuthData()
    expect(success).toBe(true)
  })
})
```

## Cleanup / التنظيف

After successful migration, old localStorage data can be cleaned up:

```typescript
import { storeMigration } from '@/lib/migration-utility'

// Clean up old localStorage keys
storeMigration.cleanupOldData()
```

**Warning**: Only run cleanup after verifying migration success and data integrity.

## Rollback Plan / خطة التراجع

If migration fails or issues arise:

1. **Keep Old Stores**: Original stores remain available
2. **Revert Imports**: Change imports back to old stores
3. **Restore Data**: Old localStorage data preserved until cleanup
4. **Fix Issues**: Address migration problems and retry

## Support / الدعم

For migration issues:
1. Check migration status and error messages
2. Verify Supabase configuration
3. Test with fallback mode
4. Review database logs in Supabase Dashboard
5. Check network connectivity and permissions

## Next Steps / الخطوات التالية

After successful migration:
1. Update all component imports to use v2 stores
2. Test all functionality thoroughly
3. Monitor database performance
4. Implement additional RLS policies if needed
5. Consider removing old store files after verification
