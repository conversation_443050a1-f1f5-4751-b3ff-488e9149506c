-- إصلاح مشكلة استرجاع بيانات المستخدمين
-- Fix Users Data Retrieval Issue
-- Ya<PERSON>in Al-Sham Project

-- ========================================
-- تشخيص المشكلة الحالية
-- ========================================

SELECT '🔍 تشخيص مشكلة استرجاع بيانات المستخدمين' as diagnosis_title;

-- فحص حالة RLS
SELECT 
    'حالة RLS على جدول المستخدمين:' as check_name,
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN '✅ مفعل'
        ELSE '❌ غير مفعل'
    END as status
FROM pg_tables 
WHERE schemaname = 'public' AND tablename = 'users';

-- فحص السياسات الحالية
SELECT 
    '🔍 السياسات الحالية على جدول المستخدمين:' as current_policies_title,
    policyname as policy_name,
    cmd as operation,
    permissive as type,
    roles as roles,
    qual as using_condition,
    with_check as check_condition
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public'
ORDER BY cmd, policyname;

-- فحص المستخدمين الموجودين (من منظور المشرف)
SELECT 
    '📊 إحصائيات المستخدمين الموجودين:' as stats_title,
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_count,
    COUNT(*) FILTER (WHERE role = 'client') as client_count,
    COUNT(*) FILTER (WHERE role = 'worker') as worker_count
FROM public.users;

-- اختبار الوصول الحالي
SELECT 
    '🧪 اختبار الوصول الحالي:' as access_test_title,
    auth.uid() as current_auth_uid,
    CASE 
        WHEN auth.uid() IS NULL THEN 'غير مسجل دخول (SQL Editor)'
        ELSE 'مسجل دخول'
    END as auth_status;

-- ========================================
-- إصلاح السياسات - المرحلة 1: حذف السياسات المتضاربة
-- ========================================

SELECT '🔧 المرحلة 1: حذف السياسات المتضاربة' as phase1_title;

-- حذف جميع السياسات الموجودة على جدول المستخدمين
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can update all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Enable read for authenticated users" ON public.users;
DROP POLICY IF EXISTS "Enable insert for admins" ON public.users;
DROP POLICY IF EXISTS "Enable update for users and admins" ON public.users;

-- ========================================
-- إصلاح السياسات - المرحلة 2: إنشاء سياسات جديدة محسنة
-- ========================================

SELECT '🔧 المرحلة 2: إنشاء سياسات جديدة محسنة' as phase2_title;

-- سياسة القراءة: المستخدمون يمكنهم رؤية ملفاتهم الشخصية
CREATE POLICY "users_select_own_profile" ON public.users
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND id = auth.uid()
    );

-- سياسة القراءة: المشرفون يمكنهم رؤية جميع المستخدمين
-- نستخدم جدول منفصل لتجنب الاستعلام الدائري
CREATE POLICY "admins_select_all_users" ON public.users
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        auth.uid() IN (
            SELECT id FROM public.users 
            WHERE role = 'admin' AND id = auth.uid()
        )
    );

-- سياسة التحديث: المستخدمون يمكنهم تحديث ملفاتهم الشخصية
CREATE POLICY "users_update_own_profile" ON public.users
    FOR UPDATE USING (
        auth.uid() IS NOT NULL AND id = auth.uid()
    );

-- سياسة التحديث: المشرفون يمكنهم تحديث جميع المستخدمين
CREATE POLICY "admins_update_all_users" ON public.users
    FOR UPDATE USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسة الإدراج: المشرفون يمكنهم إنشاء مستخدمين جدد
CREATE POLICY "admins_insert_users" ON public.users
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسة الإدراج: السماح بإنشاء المستخدم الأول (للإعداد الأولي)
CREATE POLICY "allow_first_user_creation" ON public.users
    FOR INSERT WITH CHECK (
        (SELECT COUNT(*) FROM public.users WHERE role = 'admin') = 0
    );

-- ========================================
-- إصلاح السياسات - المرحلة 3: سياسات خاصة للتطبيق
-- ========================================

SELECT '🔧 المرحلة 3: سياسات خاصة للتطبيق' as phase3_title;

-- سياسة خاصة: السماح للعمال برؤية بيانات العملاء (للطلبات)
CREATE POLICY "workers_view_clients_for_orders" ON public.users
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        role = 'client' AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'worker'
        )
    );

-- سياسة خاصة: السماح للعملاء برؤية بيانات العمال (للاختيار)
CREATE POLICY "clients_view_workers" ON public.users
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        role = 'worker' AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'client'
        )
    );

-- ========================================
-- التحقق من السياسات الجديدة
-- ========================================

SELECT '✅ التحقق من السياسات الجديدة' as verification_title;

-- عرض جميع السياسات الجديدة
SELECT 
    '📋 السياسات الجديدة على جدول المستخدمين:' as new_policies_title,
    policyname as policy_name,
    cmd as operation,
    permissive as type,
    qual as using_condition,
    with_check as check_condition
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public'
ORDER BY cmd, policyname;

-- ========================================
-- اختبار الوصول بعد الإصلاح
-- ========================================

SELECT '🧪 اختبار الوصول بعد الإصلاح' as access_test_title;

-- اختبار قراءة المستخدمين
SELECT 
    'اختبار قراءة المستخدمين:' as test_name,
    COUNT(*) as visible_users_count,
    COUNT(*) FILTER (WHERE role = 'admin') as visible_admins,
    COUNT(*) FILTER (WHERE role = 'client') as visible_clients,
    COUNT(*) FILTER (WHERE role = 'worker') as visible_workers
FROM public.users;

-- ========================================
-- إنشاء وظائف مساعدة للتحقق من الأدوار
-- ========================================

SELECT '🔧 إنشاء وظائف مساعدة' as helper_functions_title;

-- وظيفة للتحقق من كون المستخدم مشرفاً
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- وظيفة للحصول على دور المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role FROM public.users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- وظيفة للحصول على بيانات المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_data()
RETURNS TABLE(
    user_id UUID,
    user_email VARCHAR(255),
    user_name VARCHAR(255),
    user_role VARCHAR(20),
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        id,
        email,
        full_name,
        role,
        users.is_active
    FROM public.users
    WHERE id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- اختبار الوظائف المساعدة
-- ========================================

SELECT '🧪 اختبار الوظائف المساعدة' as helper_test_title;

-- اختبار وظيفة التحقق من المشرف
SELECT 
    'اختبار وظيفة is_admin:' as test_name,
    is_admin() as current_user_is_admin,
    get_current_user_role() as current_user_role;

-- اختبار وظيفة الحصول على بيانات المستخدم
SELECT 
    'بيانات المستخدم الحالي:' as current_user_data_title,
    *
FROM get_current_user_data();

-- ========================================
-- ملخص الإصلاح
-- ========================================

SELECT '========================================' as separator;
SELECT '📋 ملخص إصلاح استرجاع بيانات المستخدمين' as summary_title;
SELECT '========================================' as separator;

SELECT 
    '✅ تم إصلاح سياسات RLS بنجاح' as fix_status,
    'المستخدمين المرئيين: ' || (SELECT COUNT(*) FROM public.users) as visible_users,
    'السياسات المُنشأة: ' || (
        SELECT COUNT(*) FROM pg_policies 
        WHERE tablename = 'users' AND schemaname = 'public'
    ) as policies_created,
    'الوظائف المساعدة: 3' as helper_functions;

-- ========================================
-- تعليمات الاختبار في التطبيق
-- ========================================

SELECT '📱 تعليمات الاختبار في التطبيق' as app_testing_title;

SELECT '1. سجل دخول في التطبيق كمشرف' as step1;
SELECT '2. جرب استعلام: supabase.from("users").select("*")' as step2;
SELECT '3. يجب أن ترى جميع المستخدمين إذا كنت مشرفاً' as step3;
SELECT '4. جرب استعلام: supabase.rpc("get_current_user_data")' as step4;
SELECT '5. يجب أن ترى بياناتك الشخصية' as step5;

SELECT '🎉 إصلاح استرجاع بيانات المستخدمين مكتمل!' as completion_message;
