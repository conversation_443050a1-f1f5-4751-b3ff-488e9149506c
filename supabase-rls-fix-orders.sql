-- ياسمين الشام - إصلاح سياسات RLS للطلبات لدعم إنشاء الطلبات من قبل المشرفين
-- Ya<PERSON><PERSON> - RLS Policy Fix for Admin Order Creation
-- تاريخ الإنشاء: 2025-01-11

-- ========================================
-- إصلاح جدول الطلبات لدعم إنشاء الطلبات من قبل المشرفين
-- ========================================

-- أولاً: إزالة السياسات الحالية للطلبات
DROP POLICY IF EXISTS "Clients can view their own orders" ON public.orders;
DROP POLICY IF EXISTS "Clients can create orders" ON public.orders;
DROP POLICY IF EXISTS "Workers can view their assigned orders" ON public.orders;
DROP POLICY IF EXISTS "Workers can update their assigned orders" ON public.orders;
DROP POLICY IF EXISTS "Admins can manage all orders" ON public.orders;

-- ثانياً: إضافة حقول إضافية لتحسين إدارة الطلبات
ALTER TABLE public.orders 
  ADD COLUMN IF NOT EXISTS created_by_admin_id UUID REFERENCES public.users(id),
  ADD COLUMN IF NOT EXISTS admin_notes TEXT;

-- إضافة فهرس للمشرف الذي أنشأ الطلب
CREATE INDEX IF NOT EXISTS idx_orders_created_by_admin 
ON public.orders(created_by_admin_id) 
WHERE created_by_admin_id IS NOT NULL;

-- ثالثاً: إنشاء سياسات RLS جديدة للطلبات

-- 1. سياسة القراءة - العملاء يمكنهم رؤية طلباتهم فقط
CREATE POLICY "Clients can view their own orders" 
ON public.orders FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND client_id = auth.uid()
);

-- 2. سياسة القراءة - العمال يمكنهم رؤية الطلبات المخصصة لهم
CREATE POLICY "Workers can view their assigned orders" 
ON public.orders FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.workers 
    WHERE id = orders.worker_id AND user_id = auth.uid()
  )
);

-- 3. سياسة القراءة - المشرفون يمكنهم رؤية جميع الطلبات
CREATE POLICY "Admins can view all orders" 
ON public.orders FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- 4. سياسة الإنشاء - المشرفون فقط يمكنهم إنشاء طلبات (الأهم!)
CREATE POLICY "Admins can create orders for any client" 
ON public.orders FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- 5. سياسة التحديث - العملاء يمكنهم تحديث ملاحظاتهم فقط
CREATE POLICY "Clients can update their order notes" 
ON public.orders FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND client_id = auth.uid()
)
WITH CHECK (
  auth.uid() IS NOT NULL AND client_id = auth.uid()
);

-- 6. سياسة التحديث - العمال يمكنهم تحديث طلباتهم المخصصة
CREATE POLICY "Workers can update their assigned orders" 
ON public.orders FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.workers 
    WHERE id = orders.worker_id AND user_id = auth.uid()
  )
)
WITH CHECK (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.workers 
    WHERE id = orders.worker_id AND user_id = auth.uid()
  )
);

-- 7. سياسة التحديث - المشرفون يمكنهم تحديث جميع الطلبات
CREATE POLICY "Admins can update all orders" 
ON public.orders FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
)
WITH CHECK (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- 8. سياسة الحذف - المشرفون فقط يمكنهم حذف الطلبات
CREATE POLICY "Admins can delete orders" 
ON public.orders FOR DELETE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- ========================================
-- إصلاح سياسات عناصر الطلب (Order Items)
-- ========================================

-- إزالة السياسة الحالية لعناصر الطلب
DROP POLICY IF EXISTS "Users can view order items for their orders" ON public.order_items;

-- سياسات جديدة لعناصر الطلب
CREATE POLICY "Users can view order items for accessible orders" 
ON public.order_items FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.orders
    WHERE id = order_items.order_id
    AND (
      -- العميل يمكنه رؤية عناصر طلباته
      (client_id = auth.uid()) OR
      -- العامل يمكنه رؤية عناصر طلباته المخصصة
      (EXISTS (SELECT 1 FROM public.workers WHERE id = orders.worker_id AND user_id = auth.uid())) OR
      -- المشرف يمكنه رؤية جميع عناصر الطلبات
      (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'))
    )
  )
);

CREATE POLICY "Admins can manage order items" 
ON public.order_items FOR ALL 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- ========================================
-- وظائف مساعدة للطلبات
-- ========================================

-- إزالة الوظائف الموجودة لتجنب تضارب التوقيعات
-- استخدام طريقة آمنة لإزالة الوظائف مع جميع التوقيعات المحتملة

-- إزالة وظيفة توليد رقم الطلب بجميع التوقيعات المحتملة
DO $$
BEGIN
    -- إزالة جميع إصدارات generate_order_number
    DROP FUNCTION IF EXISTS generate_order_number() CASCADE;
    DROP FUNCTION IF EXISTS generate_order_number(TEXT) CASCADE;
    DROP FUNCTION IF EXISTS generate_order_number(INTEGER) CASCADE;

    -- إزالة جميع إصدارات create_admin_order (التوقيع القديم والجديد)
    DROP FUNCTION IF EXISTS create_admin_order(UUID, UUID, VARCHAR, VARCHAR, DECIMAL, DECIMAL, DECIMAL, DECIMAL, DECIMAL, DECIMAL, DECIMAL, VARCHAR, DATE, TEXT, TEXT, JSONB, TEXT, BOOLEAN, DECIMAL) CASCADE;
    DROP FUNCTION IF EXISTS create_admin_order(UUID, UUID, VARCHAR(20), VARCHAR(20), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), VARCHAR(50), DATE, TEXT, TEXT, JSONB, TEXT, BOOLEAN, DECIMAL(10,2)) CASCADE;
    DROP FUNCTION IF EXISTS create_admin_order(UUID, VARCHAR(20), VARCHAR(20), DECIMAL(10,2), DECIMAL(10,2), UUID, DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), VARCHAR(50), DATE, TEXT, TEXT, JSONB, TEXT, BOOLEAN, DECIMAL(10,2)) CASCADE;

    RAISE NOTICE '✅ تم حذف الوظائف الموجودة بنجاح';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ تحذير: بعض الوظائف لم تكن موجودة مسبقاً';
END $$;

-- وظيفة لتوليد رقم طلب تلقائي
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS VARCHAR(50)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_year TEXT;
  order_count INTEGER;
  new_order_number VARCHAR(50);
BEGIN
  -- الحصول على السنة الحالية
  current_year := EXTRACT(YEAR FROM NOW())::TEXT;
  
  -- عد الطلبات في السنة الحالية
  SELECT COUNT(*) INTO order_count
  FROM public.orders
  WHERE order_number LIKE current_year || '%';
  
  -- إنشاء رقم الطلب الجديد
  new_order_number := current_year || LPAD((order_count + 1)::TEXT, 4, '0');
  
  RETURN new_order_number;
END;
$$;

-- وظيفة لإنشاء طلب جديد من قبل المشرف
CREATE OR REPLACE FUNCTION create_admin_order(
  -- المعاملات المطلوبة (بدون قيم افتراضية)
  p_client_id UUID,
  p_order_type VARCHAR(20),
  p_priority VARCHAR(20),
  p_subtotal DECIMAL(10,2),
  p_total_amount DECIMAL(10,2),
  -- المعاملات الاختيارية (مع قيم افتراضية)
  p_worker_id UUID DEFAULT NULL,
  p_fabric_cost DECIMAL(10,2) DEFAULT 0,
  p_labor_cost DECIMAL(10,2) DEFAULT 0,
  p_additional_costs DECIMAL(10,2) DEFAULT 0,
  p_discount_amount DECIMAL(10,2) DEFAULT 0,
  p_tax_amount DECIMAL(10,2) DEFAULT 0,
  p_payment_method VARCHAR(50) DEFAULT NULL,
  p_estimated_completion_date DATE DEFAULT NULL,
  p_client_notes TEXT DEFAULT NULL,
  p_admin_notes TEXT DEFAULT NULL,
  p_measurements JSONB DEFAULT NULL,
  p_special_instructions TEXT DEFAULT NULL,
  p_rush_order BOOLEAN DEFAULT false,
  p_rush_fee DECIMAL(10,2) DEFAULT 0
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  new_order_id UUID;
  new_order_number VARCHAR(50);
  current_admin_id UUID;
BEGIN
  -- التحقق من أن المستخدم الحالي مشرف
  SELECT auth.uid() INTO current_admin_id;
  
  IF NOT EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = current_admin_id AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'غير مسموح: المستخدم ليس مشرفاً';
  END IF;
  
  -- التحقق من وجود العميل
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = p_client_id) THEN
    RAISE EXCEPTION 'العميل المحدد غير موجود';
  END IF;
  
  -- التحقق من وجود العامل (إذا تم تحديده)
  IF p_worker_id IS NOT NULL AND NOT EXISTS (
    SELECT 1 FROM public.workers WHERE id = p_worker_id
  ) THEN
    RAISE EXCEPTION 'العامل المحدد غير موجود';
  END IF;
  
  -- توليد رقم طلب جديد
  SELECT generate_order_number() INTO new_order_number;
  
  -- إنشاء الطلب
  INSERT INTO public.orders (
    order_number,
    client_id,
    worker_id,
    order_type,
    priority,
    subtotal,
    fabric_cost,
    labor_cost,
    additional_costs,
    discount_amount,
    tax_amount,
    total_amount,
    payment_method,
    estimated_completion_date,
    client_notes,
    admin_notes,
    measurements,
    special_instructions,
    rush_order,
    rush_fee,
    created_by_admin_id,
    status
  ) VALUES (
    new_order_number,
    p_client_id,
    p_worker_id,
    p_order_type,
    p_priority,
    p_subtotal,
    p_fabric_cost,
    p_labor_cost,
    p_additional_costs,
    p_discount_amount,
    p_tax_amount,
    p_total_amount,
    p_payment_method,
    p_estimated_completion_date,
    p_client_notes,
    p_admin_notes,
    p_measurements,
    p_special_instructions,
    p_rush_order,
    p_rush_fee,
    current_admin_id,
    'pending'
  ) RETURNING id INTO new_order_id;
  
  RETURN new_order_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- فهارس إضافية لتحسين الأداء
-- ========================================

-- فهرس للطلبات حسب الحالة والتاريخ
CREATE INDEX IF NOT EXISTS idx_orders_status_created 
ON public.orders(status, created_at DESC);

-- فهرس للطلبات حسب العميل والحالة
CREATE INDEX IF NOT EXISTS idx_orders_client_status 
ON public.orders(client_id, status);

-- فهرس للطلبات حسب العامل والحالة
CREATE INDEX IF NOT EXISTS idx_orders_worker_status 
ON public.orders(worker_id, status) 
WHERE worker_id IS NOT NULL;

-- فهرس لرقم الطلب (للبحث السريع)
CREATE INDEX IF NOT EXISTS idx_orders_order_number 
ON public.orders(order_number);

-- ========================================
-- منح الصلاحيات للوظائف
-- ========================================

-- السماح للمشرفين باستخدام وظيفة إنشاء الطلبات
GRANT EXECUTE ON FUNCTION create_admin_order(
  UUID, VARCHAR(20), VARCHAR(20), DECIMAL(10,2), DECIMAL(10,2), UUID,
  DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2), DECIMAL(10,2),
  VARCHAR(50), DATE, TEXT, TEXT, JSONB, TEXT, BOOLEAN, DECIMAL(10,2)
) TO authenticated;

-- السماح للجميع باستخدام وظيفة توليد رقم الطلب
GRANT EXECUTE ON FUNCTION generate_order_number() TO authenticated;

-- ========================================
-- التحقق من إنشاء الوظائف بنجاح
-- ========================================

DO $$
BEGIN
    -- التحقق من وجود وظيفة توليد رقم الطلب
    IF EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name = 'generate_order_number'
        AND routine_type = 'FUNCTION'
    ) THEN
        RAISE NOTICE '✅ وظيفة generate_order_number تم إنشاؤها بنجاح';
    ELSE
        RAISE EXCEPTION '❌ فشل في إنشاء وظيفة generate_order_number';
    END IF;

    -- التحقق من وجود وظيفة إنشاء الطلب
    IF EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name = 'create_admin_order'
        AND routine_type = 'FUNCTION'
    ) THEN
        RAISE NOTICE '✅ وظيفة create_admin_order تم إنشاؤها بنجاح';
    ELSE
        RAISE EXCEPTION '❌ فشل في إنشاء وظيفة create_admin_order';
    END IF;

    RAISE NOTICE '🎉 جميع الوظائف تم إنشاؤها بنجاح!';
END $$;

-- ========================================
-- رسائل النجاح
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إصلاح سياسات RLS للطلبات بنجاح!';
    RAISE NOTICE '🎯 الآن يمكن للمشرفين إنشاء طلبات للعملاء';
    RAISE NOTICE '🔒 تم الحفاظ على الأمان للمستخدمين والعمال';
    RAISE NOTICE '⚡ تم إضافة فهارس لتحسين الأداء';
    RAISE NOTICE '🛠️ تم إضافة وظائف مساعدة لإدارة الطلبات';
    RAISE NOTICE '📊 النظام جاهز لإنشاء الطلبات من قبل المشرفين';
END $$;
