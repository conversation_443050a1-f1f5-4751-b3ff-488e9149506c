// Enhanced Authentication Store with Supabase Integration
// متجر المصادقة المحسن مع تكامل Supabase

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { auth, isSupabaseConfigured } from '@/lib/supabase'
import { userService } from '@/lib/database-v2'

// تعريف نوع المستخدم المحسن
export interface AuthUser {
  id: string
  email: string
  full_name: string
  phone?: string
  avatar_url?: string
  role: 'admin' | 'worker' | 'client'
  is_active: boolean
  email_verified: boolean
  phone_verified: boolean
  preferences: Record<string, any>
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  // Supabase auth data
  supabase_user_id?: string
  session?: any
}

interface AuthState {
  user: AuthUser | null
  isLoading: boolean
  error: string | null
  isInitialized: boolean

  // Actions
  signIn: (email: string, password: string) => Promise<boolean>
  signOut: () => Promise<void>
  setUser: (user: AuthUser | null) => void
  clearError: () => void
  checkAuth: () => Promise<void>
  isAuthenticated: () => boolean
  initialize: () => Promise<void>
  
  // Enhanced user management
  updateProfile: (updates: Partial<AuthUser>) => Promise<boolean>
  refreshUser: () => Promise<void>
}

// Fallback users for development when Supabase is not configured
const getFallbackUsers = () => [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    full_name: 'مدير النظام',
    role: 'admin' as const,
    is_active: true,
    phone: '+963-11-1234567',
    email_verified: true,
    phone_verified: true,
    preferences: {},
    metadata: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'worker123',
    full_name: 'عاملة التفصيل',
    role: 'worker' as const,
    is_active: true,
    phone: '+963-11-7654321',
    email_verified: true,
    phone_verified: true,
    preferences: {},
    metadata: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false,
      error: null,
      isInitialized: false,

      // Initialize authentication state
      initialize: async () => {
        if (get().isInitialized) return
        
        set({ isLoading: true })
        
        try {
          if (isSupabaseConfigured()) {
            // Check for existing Supabase session
            const { session, error } = await auth.getSession()
            
            if (error) {
              console.warn('Session check error:', error)
              set({ isInitialized: true, isLoading: false })
              return
            }
            
            if (session?.user) {
              // Get user profile from database
              const { user: dbUser, error: userError } = await userService.getUserById(session.user.id)
              
              if (dbUser && !userError) {
                const authUser: AuthUser = {
                  ...dbUser,
                  supabase_user_id: session.user.id,
                  session
                }
                set({ user: authUser, isInitialized: true, isLoading: false })
                return
              }
            }
          }
          
          // Fallback: check for persisted user data
          set({ isInitialized: true, isLoading: false })
        } catch (error) {
          console.error('Auth initialization error:', error)
          set({ isInitialized: true, isLoading: false, error: 'خطأ في تهيئة المصادقة' })
        }
      },

      signIn: async (email: string, password: string) => {
        set({ isLoading: true, error: null })

        try {
          if (isSupabaseConfigured()) {
            // Use Supabase authentication
            const { user: supabaseUser, session, error } = await auth.signIn(email, password)
            
            if (error) {
              set({ isLoading: false, error: error.message || 'خطأ في تسجيل الدخول' })
              return false
            }
            
            if (supabaseUser && session) {
              // Get user profile from database
              const { user: dbUser, error: userError } = await userService.getUserById(supabaseUser.id)
              
              if (userError || !dbUser) {
                set({ isLoading: false, error: 'لم يتم العثور على ملف المستخدم' })
                return false
              }
              
              const authUser: AuthUser = {
                ...dbUser,
                supabase_user_id: supabaseUser.id,
                session
              }
              
              set({ user: authUser, isLoading: false })
              return true
            }
          } else {
            // Fallback to mock authentication for development
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            const users = getFallbackUsers()
            const foundUser = users.find(
              user => user.email.toLowerCase() === email.toLowerCase() && user.password === password
            )
            
            if (foundUser) {
              const { password: _, ...userWithoutPassword } = foundUser
              const authUser: AuthUser = userWithoutPassword
              
              set({ user: authUser, isLoading: false })
              return true
            } else {
              set({ isLoading: false, error: 'بيانات الدخول غير صحيحة' })
              return false
            }
          }
          
          set({ isLoading: false, error: 'خطأ غير متوقع في تسجيل الدخول' })
          return false
        } catch (error) {
          console.error('Sign in error:', error)
          set({ isLoading: false, error: 'خطأ في تسجيل الدخول' })
          return false
        }
      },

      signOut: async () => {
        set({ isLoading: true })

        try {
          if (isSupabaseConfigured()) {
            const { error } = await auth.signOut()
            if (error) {
              console.warn('Sign out error:', error)
            }
          }
          
          set({ user: null, isLoading: false, error: null })
        } catch (error) {
          console.error('Sign out error:', error)
          set({ user: null, isLoading: false, error: 'خطأ في تسجيل الخروج' })
        }
      },

      setUser: (user: AuthUser | null) => {
        set({ user })
      },

      clearError: () => {
        set({ error: null })
      },

      checkAuth: async () => {
        const state = get()
        if (!state.isInitialized) {
          await state.initialize()
        }
      },

      isAuthenticated: () => {
        const state = get()
        return state.user !== null && state.user.is_active
      },

      updateProfile: async (updates: Partial<AuthUser>) => {
        const currentUser = get().user
        if (!currentUser) return false
        
        set({ isLoading: true })
        
        try {
          if (isSupabaseConfigured()) {
            const { user: updatedUser, error } = await userService.updateUser(currentUser.id, updates)
            
            if (error || !updatedUser) {
              set({ isLoading: false, error: 'خطأ في تحديث الملف الشخصي' })
              return false
            }
            
            const authUser: AuthUser = {
              ...updatedUser,
              supabase_user_id: currentUser.supabase_user_id,
              session: currentUser.session
            }
            
            set({ user: authUser, isLoading: false })
            return true
          } else {
            // Fallback: update in-memory user
            const updatedUser = { ...currentUser, ...updates, updated_at: new Date().toISOString() }
            set({ user: updatedUser, isLoading: false })
            return true
          }
        } catch (error) {
          console.error('Update profile error:', error)
          set({ isLoading: false, error: 'خطأ في تحديث الملف الشخصي' })
          return false
        }
      },

      refreshUser: async () => {
        const currentUser = get().user
        if (!currentUser || !isSupabaseConfigured()) return
        
        try {
          const { user: refreshedUser, error } = await userService.getUserById(currentUser.id)
          
          if (refreshedUser && !error) {
            const authUser: AuthUser = {
              ...refreshedUser,
              supabase_user_id: currentUser.supabase_user_id,
              session: currentUser.session
            }
            set({ user: authUser })
          }
        } catch (error) {
          console.error('Refresh user error:', error)
        }
      }
    }),
    {
      name: 'yasmin-auth-storage-v2',
      partialize: (state) => ({ 
        user: state.user,
        isInitialized: state.isInitialized
      })
    }
  )
)
