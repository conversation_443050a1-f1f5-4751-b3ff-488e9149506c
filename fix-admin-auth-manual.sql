-- إصلاح صلاحيات المشرف - النسخة اليدوية
-- Manual Admin Authentication Fix
-- Ya<PERSON>in Al-Sham Project
-- Use this when auth.uid() returns NULL in SQL Editor

-- ========================================
-- فحص حالة المصادقة أولاً
-- ========================================

SELECT '🔍 فحص حالة المصادقة في SQL Editor' as check_title;

-- فحص auth.uid()
SELECT 
    'حالة المصادقة:' as check_name,
    auth.uid() as current_auth_uid,
    auth.email() as current_auth_email,
    CASE 
        WHEN auth.uid() IS NULL THEN '❌ غير مسجل دخول في SQL Editor'
        ELSE '✅ مسجل دخول'
    END as auth_status;

-- ========================================
-- الحل 1: البحث عن المشرفين الموجودين
-- ========================================

SELECT '🔍 البحث عن المشرفين الموجودين في النظام' as search_title;

-- عرض جميع المشرفين الموجودين
SELECT 
    'المشرفين الموجودين:' as info_type,
    id,
    email,
    full_name,
    role,
    created_at,
    phone
FROM public.users 
WHERE role = 'admin'
ORDER BY created_at;

-- عرض إجمالي المستخدمين
SELECT 
    'إحصائيات المستخدمين:' as stats_type,
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_count,
    COUNT(*) FILTER (WHERE role = 'client') as client_count,
    COUNT(*) FILTER (WHERE role = 'worker') as worker_count
FROM public.users;

-- ========================================
-- الحل 2: إنشاء مشرف افتراضي
-- ========================================

SELECT '🔧 إنشاء مشرف افتراضي للنظام' as create_admin_title;

-- إنشاء مشرف افتراضي بمعرف ثابت
DO $$
BEGIN
    -- التحقق من وجود المشرف أولاً
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE email = '<EMAIL>') THEN
        INSERT INTO public.users (
            id,
            email,
            full_name,
            role,
            phone,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            '<EMAIL>',
            'مشرف النظام الافتراضي',
            'admin',
            '+************',
            NOW(),
            NOW()
        );
        RAISE NOTICE '✅ تم إنشاء المشرف الافتراضي بنجاح';
    ELSE
        -- تحديث المستخدم الموجود ليصبح مشرفاً
        UPDATE public.users
        SET
            role = 'admin',
            updated_at = NOW(),
            full_name = COALESCE(full_name, 'مشرف النظام الافتراضي')
        WHERE email = '<EMAIL>';
        RAISE NOTICE '✅ تم تحديث المستخدم الموجود ليصبح مشرفاً';
    END IF;
END $$;

-- عرض المشرف المُنشأ/المُحدث
SELECT
    'المشرف الافتراضي:' as admin_info,
    id,
    email,
    full_name,
    role,
    phone,
    created_at
FROM public.users
WHERE email = '<EMAIL>';

-- ========================================
-- الحل 3: تحديث مستخدم موجود ليصبح مشرفاً
-- ========================================

-- إذا كان لديك مستخدم موجود تريد جعله مشرفاً
-- قم بتغيير الإيميل أدناه إلى إيميلك الفعلي

/*
-- استبدل '<EMAIL>' بإيميلك الفعلي
UPDATE public.users 
SET 
    role = 'admin',
    updated_at = NOW(),
    full_name = COALESCE(full_name, 'مشرف النظام')
WHERE email = '<EMAIL>'  -- ⚠️ غير هذا إلى إيميلك
RETURNING id, email, full_name, role;
*/

-- ========================================
-- الحل 4: إنشاء مشرف بإيميل محدد
-- ========================================

-- إذا كنت تعرف إيميلك في Supabase Auth
-- قم بتغيير الإيميل أدناه

/*
-- استبدل '<EMAIL>' بإيميلك الفعلي
INSERT INTO public.users (
    id,
    email,
    full_name,
    role,
    phone,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',  -- ⚠️ غير هذا إلى إيميلك
    'مشرف النظام',
    'admin',
    NULL,
    NOW(),
    NOW()
) 
ON CONFLICT (email) DO UPDATE SET
    role = 'admin',
    updated_at = NOW()
RETURNING id, email, full_name, role;
*/

-- ========================================
-- التحقق من النتيجة
-- ========================================

SELECT '✅ التحقق من المشرفين بعد الإصلاح' as verification_title;

-- عرض جميع المشرفين
SELECT 
    'المشرفين في النظام:' as admin_list,
    id,
    email,
    full_name,
    role,
    created_at
FROM public.users 
WHERE role = 'admin'
ORDER BY created_at;

-- ========================================
-- اختبار الوظائف
-- ========================================

SELECT '🧪 اختبار الوظائف' as test_title;

-- اختبار توليد رقم طلب
SELECT 
    'اختبار توليد رقم طلب:' as test_name,
    generate_order_number() as test_order_number;

-- ========================================
-- إنشاء عميل تجريبي
-- ========================================

-- إنشاء عميل تجريبي للاختبار
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE phone = '0500000002') THEN
        INSERT INTO public.users (
            full_name,
            phone,
            role,
            email,
            created_at,
            updated_at
        ) VALUES (
            'عميل تجريبي',
            '0500000002',
            'client',
            '<EMAIL>',
            NOW(),
            NOW()
        );
        RAISE NOTICE '✅ تم إنشاء العميل التجريبي';
    ELSE
        UPDATE public.users
        SET
            full_name = 'عميل تجريبي',
            updated_at = NOW()
        WHERE phone = '0500000002';
        RAISE NOTICE '✅ تم تحديث العميل التجريبي';
    END IF;
END $$;

-- عرض العميل التجريبي
SELECT
    'العميل التجريبي:' as client_info,
    id,
    full_name,
    phone,
    role,
    email
FROM public.users
WHERE phone = '0500000002';

-- ========================================
-- اختبار إنشاء طلب (يتطلب مشرف)
-- ========================================

-- ملاحظة: هذا الاختبار سيفشل إذا لم تكن مسجل دخول كمشرف
-- لكن يمكنك اختباره لاحقاً في التطبيق

/*
SELECT '🧪 اختبار إنشاء طلب' as order_test_title;

SELECT create_admin_order(
    (SELECT id FROM public.users WHERE phone = '0500000002' AND role = 'client'),
    'custom'::VARCHAR(20),
    'normal'::VARCHAR(20),
    150.00::DECIMAL(10,2),
    150.00::DECIMAL(10,2)
) as test_order_id;
*/

-- ========================================
-- إرشادات للخطوات التالية
-- ========================================

SELECT '📋 إرشادات للخطوات التالية' as instructions_title;

SELECT '1. تأكد من وجود مشرف في النظام (انظر النتائج أعلاه)' as step1;
SELECT '2. سجل دخول في تطبيق Next.js باستخدام إيميل المشرف' as step2;
SELECT '3. جرب إنشاء طلب في التطبيق' as step3;
SELECT '4. إذا فشل، تحقق من أن إيميل المشرف في التطبيق يطابق إيميل المشرف في قاعدة البيانات' as step4;

-- ========================================
-- معلومات مهمة للربط مع التطبيق
-- ========================================

SELECT '⚠️ معلومات مهمة للربط مع التطبيق' as important_info_title;

SELECT 'يجب أن يكون إيميل المستخدم في Supabase Auth مطابقاً لإيميل المشرف في جدول المستخدمين' as auth_matching_info;

-- عرض المشرفين مع إيميلاتهم للمطابقة
SELECT 
    'إيميلات المشرفين للمطابقة:' as admin_emails_title,
    email as admin_email,
    full_name,
    'استخدم هذا الإيميل لتسجيل الدخول في التطبيق' as instruction
FROM public.users 
WHERE role = 'admin'
ORDER BY created_at;

-- ========================================
-- ملخص الإصلاح
-- ========================================

SELECT '========================================' as separator;
SELECT '📋 ملخص الإصلاح اليدوي' as summary_title;
SELECT '========================================' as separator;

SELECT 
    'عدد المشرفين: ' || COUNT(*) as admin_count_summary
FROM public.users 
WHERE role = 'admin'
UNION ALL
SELECT 
    'عدد العملاء: ' || COUNT(*)
FROM public.users 
WHERE role = 'client'
UNION ALL
SELECT 
    'إجمالي المستخدمين: ' || COUNT(*)
FROM public.users;

SELECT '✅ تم إنشاء المشرف بنجاح!' as success_message;
SELECT '🔄 الآن سجل دخول في التطبيق باستخدام إيميل المشرف' as next_step;
