// Migration Utility for Yasmin Al-Sham Store Migration
// أداة الترحيل لمتجر ياسمين الشام

import { isSupabaseConfigured } from '@/lib/supabase'
import { userService, orderService, workerService } from '@/lib/database-v2'

// Migration status interface
export interface MigrationStatus {
  isComplete: boolean
  authMigrated: boolean
  ordersMigrated: boolean
  workersMigrated: boolean
  shopMigrated: boolean
  errors: string[]
  warnings: string[]
}

// Migration utility class
export class StoreMigration {
  private status: MigrationStatus = {
    isComplete: false,
    authMigrated: false,
    ordersMigrated: false,
    workersMigrated: false,
    shopMigrated: false,
    errors: [],
    warnings: []
  }

  // Check if migration is needed
  public checkMigrationNeeded(): boolean {
    const hasOldAuthData = localStorage.getItem('yasmin-users') || localStorage.getItem('yasmin-auth-user')
    const hasOldDataStore = localStorage.getItem('yasmin-data-storage')
    const hasOldShopData = localStorage.getItem('yasmin-alsham-shop')
    
    return !!(hasOldAuthData || hasOldDataStore || hasOldShopData)
  }

  // Get current migration status
  public getStatus(): MigrationStatus {
    return { ...this.status }
  }

  // Migrate authentication data
  public async migrateAuthData(): Promise<boolean> {
    try {
      console.log('Starting auth data migration...')
      
      // Check for old auth data
      const oldUsers = localStorage.getItem('yasmin-users')
      const oldCurrentUser = localStorage.getItem('yasmin-auth-user')
      
      if (!oldUsers && !oldCurrentUser) {
        this.status.authMigrated = true
        this.status.warnings.push('No old auth data found to migrate')
        return true
      }

      if (!isSupabaseConfigured()) {
        this.status.warnings.push('Supabase not configured - auth data will remain in localStorage')
        this.status.authMigrated = true
        return true
      }

      // Parse old data
      let users = []
      let currentUser = null
      
      if (oldUsers) {
        try {
          users = JSON.parse(oldUsers)
        } catch (e) {
          this.status.errors.push('Failed to parse old users data')
          return false
        }
      }
      
      if (oldCurrentUser) {
        try {
          currentUser = JSON.parse(oldCurrentUser)
        } catch (e) {
          this.status.errors.push('Failed to parse old current user data')
          return false
        }
      }

      // Migrate users to Supabase (if they don't already exist)
      for (const user of users) {
        try {
          // Check if user already exists
          const { user: existingUser } = await userService.getUserByEmail(user.email)
          
          if (!existingUser) {
            // Create user in database
            const userData = {
              email: user.email,
              full_name: user.name || user.full_name || user.email.split('@')[0],
              phone: user.phone || null,
              role: user.role || 'client',
              is_active: user.is_active !== false,
              email_verified: user.email_verified || false,
              phone_verified: user.phone_verified || false,
              preferences: user.preferences || {},
              metadata: user.metadata || {}
            }
            
            await userService.createUser(userData)
            console.log(`Migrated user: ${user.email}`)
          }
        } catch (error) {
          console.error(`Failed to migrate user ${user.email}:`, error)
          this.status.warnings.push(`Failed to migrate user: ${user.email}`)
        }
      }

      this.status.authMigrated = true
      console.log('Auth data migration completed')
      return true
    } catch (error) {
      console.error('Auth migration error:', error)
      this.status.errors.push('Auth migration failed: ' + (error as Error).message)
      return false
    }
  }

  // Migrate orders data
  public async migrateOrdersData(): Promise<boolean> {
    try {
      console.log('Starting orders data migration...')
      
      const oldDataStorage = localStorage.getItem('yasmin-data-storage')
      
      if (!oldDataStorage) {
        this.status.ordersMigrated = true
        this.status.warnings.push('No old orders data found to migrate')
        return true
      }

      if (!isSupabaseConfigured()) {
        this.status.warnings.push('Supabase not configured - orders data will remain in localStorage')
        this.status.ordersMigrated = true
        return true
      }

      // Parse old data
      let dataStore
      try {
        dataStore = JSON.parse(oldDataStorage)
      } catch (e) {
        this.status.errors.push('Failed to parse old data storage')
        return false
      }

      const orders = dataStore?.state?.orders || []
      
      // Migrate orders to Supabase
      for (const order of orders) {
        try {
          // Check if order already exists
          const { order: existingOrder } = await orderService.getOrderById(order.id)
          
          if (!existingOrder) {
            // Create order in database
            const orderData = {
              client_id: order.client_id || order.clientId,
              worker_id: order.worker_id || order.workerId,
              design_id: order.design_id || order.designId,
              fabric_id: order.fabric_id || order.fabricId,
              status: order.status || 'pending',
              priority: order.priority || 'normal',
              measurements: order.measurements || {},
              special_requests: order.special_requests || order.specialRequests || '',
              estimated_completion: order.estimated_completion || order.estimatedCompletion,
              actual_completion: order.actual_completion || order.actualCompletion,
              total_price: order.total_price || order.totalPrice || 0,
              paid_amount: order.paid_amount || order.paidAmount || 0,
              payment_status: order.payment_status || order.paymentStatus || 'pending',
              notes: order.notes || '',
              metadata: order.metadata || {}
            }
            
            await orderService.createOrder(orderData)
            console.log(`Migrated order: ${order.id}`)
          }
        } catch (error) {
          console.error(`Failed to migrate order ${order.id}:`, error)
          this.status.warnings.push(`Failed to migrate order: ${order.id}`)
        }
      }

      this.status.ordersMigrated = true
      console.log('Orders data migration completed')
      return true
    } catch (error) {
      console.error('Orders migration error:', error)
      this.status.errors.push('Orders migration failed: ' + (error as Error).message)
      return false
    }
  }

  // Migrate workers data
  public async migrateWorkersData(): Promise<boolean> {
    try {
      console.log('Starting workers data migration...')
      
      const oldDataStorage = localStorage.getItem('yasmin-data-storage')
      
      if (!oldDataStorage) {
        this.status.workersMigrated = true
        this.status.warnings.push('No old workers data found to migrate')
        return true
      }

      if (!isSupabaseConfigured()) {
        this.status.warnings.push('Supabase not configured - workers data will remain in localStorage')
        this.status.workersMigrated = true
        return true
      }

      // Parse old data
      let dataStore
      try {
        dataStore = JSON.parse(oldDataStorage)
      } catch (e) {
        this.status.errors.push('Failed to parse old data storage')
        return false
      }

      const workers = dataStore?.state?.workers || []
      
      // Migrate workers to Supabase
      for (const worker of workers) {
        try {
          // Check if worker already exists
          const { worker: existingWorker } = await workerService.getWorkerById(worker.id)
          
          if (!existingWorker) {
            // Create worker in database
            const workerData = {
              user_id: worker.user_id || worker.userId,
              specialties: worker.specialties || [],
              experience_years: worker.experience_years || worker.experienceYears || 0,
              hourly_rate: worker.hourly_rate || worker.hourlyRate || 0,
              availability_schedule: worker.availability_schedule || worker.availabilitySchedule || {},
              is_available: worker.is_available !== false,
              performance_rating: worker.performance_rating || worker.performanceRating || 0,
              completed_orders: worker.completed_orders || worker.completedOrders || 0,
              notes: worker.notes || '',
              metadata: worker.metadata || {}
            }
            
            await workerService.createWorker(workerData)
            console.log(`Migrated worker: ${worker.id}`)
          }
        } catch (error) {
          console.error(`Failed to migrate worker ${worker.id}:`, error)
          this.status.warnings.push(`Failed to migrate worker: ${worker.id}`)
        }
      }

      this.status.workersMigrated = true
      console.log('Workers data migration completed')
      return true
    } catch (error) {
      console.error('Workers migration error:', error)
      this.status.errors.push('Workers migration failed: ' + (error as Error).message)
      return false
    }
  }

  // Migrate shop data (favorites and cart)
  public migrateShopData(): boolean {
    try {
      console.log('Starting shop data migration...')
      
      const oldShopData = localStorage.getItem('yasmin-alsham-shop')
      
      if (!oldShopData) {
        this.status.shopMigrated = true
        this.status.warnings.push('No old shop data found to migrate')
        return true
      }

      // Parse old data
      let shopStore
      try {
        shopStore = JSON.parse(oldShopData)
      } catch (e) {
        this.status.errors.push('Failed to parse old shop data')
        return false
      }

      const state = shopStore?.state
      
      if (state) {
        // Create new shop data in new format
        const newShopData = {
          state: {
            favorites: state.favorites || [],
            cart: state.cart || [],
            isLoading: false,
            isFavoritesLoading: false,
            isCartLoading: false,
            error: null,
            favoritesError: null,
            cartError: null
          },
          version: 2
        }
        
        // Save to new localStorage key
        localStorage.setItem('yasmin-alsham-shop-v2', JSON.stringify(newShopData))
        console.log('Shop data migrated to new format')
      }

      this.status.shopMigrated = true
      console.log('Shop data migration completed')
      return true
    } catch (error) {
      console.error('Shop migration error:', error)
      this.status.errors.push('Shop migration failed: ' + (error as Error).message)
      return false
    }
  }

  // Run complete migration
  public async runFullMigration(): Promise<MigrationStatus> {
    console.log('Starting full store migration...')
    
    // Reset status
    this.status = {
      isComplete: false,
      authMigrated: false,
      ordersMigrated: false,
      workersMigrated: false,
      shopMigrated: false,
      errors: [],
      warnings: []
    }

    // Run migrations in sequence
    await this.migrateAuthData()
    await this.migrateOrdersData()
    await this.migrateWorkersData()
    this.migrateShopData()

    // Check if all migrations completed
    this.status.isComplete = 
      this.status.authMigrated && 
      this.status.ordersMigrated && 
      this.status.workersMigrated && 
      this.status.shopMigrated

    if (this.status.isComplete) {
      console.log('Full migration completed successfully')
    } else {
      console.log('Migration completed with some issues')
    }

    return this.getStatus()
  }

  // Clean up old data (use with caution)
  public cleanupOldData(): void {
    const oldKeys = [
      'yasmin-users',
      'yasmin-auth-user', 
      'yasmin-data-storage',
      'yasmin-alsham-shop'
    ]
    
    oldKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key)
        console.log(`Removed old localStorage key: ${key}`)
      }
    })
  }
}

// Export singleton instance
export const storeMigration = new StoreMigration()

// Helper function to check if migration is needed
export const isMigrationNeeded = (): boolean => {
  return storeMigration.checkMigrationNeeded()
}

// Helper function to run migration
export const runMigration = async (): Promise<MigrationStatus> => {
  return await storeMigration.runFullMigration()
}
