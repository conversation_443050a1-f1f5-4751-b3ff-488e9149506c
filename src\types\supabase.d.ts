// Enhanced Supabase type declarations for better module resolution
// تعريفات أنواع Supabase المحسنة لحل مشاكل الوحدات

declare module '@supabase/supabase-js' {
  export interface SupabaseClient {
    auth: any;
    from: (table: string) => any;
    storage: any;
    functions: any;
    realtime: any;
  }

  export interface SupabaseClientOptions {
    auth?: {
      persistSession?: boolean;
      autoRefreshToken?: boolean;
      detectSessionInUrl?: boolean;
      flowType?: 'implicit' | 'pkce';
    };
    global?: {
      headers?: Record<string, string>;
    };
    db?: {
      schema?: string;
    };
    realtime?: {
      params?: Record<string, any>;
    };
  }

  export function createClient(
    supabaseUrl: string,
    supabaseKey: string,
    options?: SupabaseClientOptions
  ): SupabaseClient;

  export interface User {
    id: string;
    email?: string;
    phone?: string;
    created_at: string;
    updated_at: string;
    email_confirmed_at?: string;
    phone_confirmed_at?: string;
    last_sign_in_at?: string;
    role?: string;
    user_metadata?: Record<string, any>;
    app_metadata?: Record<string, any>;
  }

  export interface Session {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    user: User;
  }

  export interface AuthResponse {
    data: {
      user: User | null;
      session: Session | null;
    };
    error: Error | null;
  }

  export interface PostgrestResponse<T> {
    data: T | null;
    error: Error | null;
    count?: number;
    status: number;
    statusText: string;
  }

  export interface PostgrestSingleResponse<T> {
    data: T | null;
    error: Error | null;
    status: number;
    statusText: string;
  }
}

// Global type augmentation for better compatibility
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_SUPABASE_URL: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
    }
  }
}

// Module resolution fallback types
declare module '*/vendor-chunks/@supabase*' {
  export * from '@supabase/supabase-js';
}

declare module '*/@supabase*' {
  export * from '@supabase/supabase-js';
}

export {};
