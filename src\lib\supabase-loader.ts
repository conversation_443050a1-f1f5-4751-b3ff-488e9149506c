// Enhanced Supabase loader with multiple fallback strategies
// محمل Supabase المحسن مع استراتيجيات احتياطية متعددة

import type { SupabaseClient } from '@supabase/supabase-js';

// Multiple import strategies for better compatibility
let createClientFunction: any = null;
let importError: string | null = null;

// Strategy 1: Standard ES6 import
async function loadSupabaseES6() {
  try {
    const module = await import('@supabase/supabase-js');
    return module.createClient;
  } catch (error) {
    console.warn('ES6 import failed:', error);
    return null;
  }
}

// Strategy 2: CommonJS require
function loadSupabaseCommonJS() {
  try {
    const module = require('@supabase/supabase-js');
    return module.createClient;
  } catch (error) {
    console.warn('CommonJS require failed:', error);
    return null;
  }
}

// Strategy 3: Direct module access
function loadSupabaseDirect() {
  try {
    // Direct access to avoid dynamic require warnings
    const supabaseModule = eval('require')('@supabase/supabase-js');
    return supabaseModule.createClient;
  } catch (error) {
    console.warn('Direct access failed:', error);
    return null;
  }
}

// Initialize the loader
async function initializeSupabaseLoader() {
  if (createClientFunction) {
    return createClientFunction;
  }

  // Try different loading strategies
  const strategies = [
    loadSupabaseCommonJS,
    loadSupabaseDirect,
    loadSupabaseES6
  ];

  for (const strategy of strategies) {
    try {
      const result = await strategy();
      if (result) {
        createClientFunction = result;
        console.log('✅ Supabase loaded successfully');
        return result;
      }
    } catch (error) {
      console.warn('Strategy failed:', error);
    }
  }

  importError = 'All Supabase loading strategies failed';
  console.error('❌ Failed to load Supabase client');
  return null;
}

// Enhanced client creation with better error handling
export async function createSupabaseClient(
  url: string,
  key: string,
  options?: any
): Promise<SupabaseClient | null> {
  const createClient = await initializeSupabaseLoader();
  
  if (!createClient) {
    console.error('Supabase createClient not available:', importError);
    return null;
  }

  try {
    return createClient(url, key, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      global: {
        headers: {
          'X-Client-Info': 'yasmin-alsham@1.0.0',
        },
      },
      ...options,
    });
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    return null;
  }
}

// Synchronous version for immediate use
export function createSupabaseClientSync(
  url: string,
  key: string,
  options?: any
): SupabaseClient | null {
  if (!createClientFunction) {
    // Try to load synchronously first
    createClientFunction = loadSupabaseCommonJS() || loadSupabaseDirect();
  }

  if (!createClientFunction) {
    console.error('Supabase createClient not available for sync creation');
    return null;
  }

  try {
    return createClientFunction(url, key, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      global: {
        headers: {
          'X-Client-Info': 'yasmin-alsham@1.0.0',
        },
      },
      ...options,
    });
  } catch (error) {
    console.error('Failed to create Supabase client sync:', error);
    return null;
  }
}

// Check if Supabase is available
export function isSupabaseAvailable(): boolean {
  return createClientFunction !== null;
}

// Get loading status
export function getSupabaseLoadingStatus() {
  return {
    loaded: createClientFunction !== null,
    error: importError,
  };
}

// Preload Supabase (call this early in your app)
export async function preloadSupabase() {
  await initializeSupabaseLoader();
}

export default {
  createSupabaseClient,
  createSupabaseClientSync,
  isSupabaseAvailable,
  getSupabaseLoadingStatus,
  preloadSupabase,
};
