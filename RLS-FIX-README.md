# 🔧 إصلاح سياسات RLS للمواعيد المجهولة - ياسمين الشام

## 📋 ملخص المشكلة

كانت المشكلة الأساسية في سياسات Row Level Security (RLS) لجدول المواعيد، حيث كانت تتطلب أن يكون المستخدم مسجل الدخول (`auth.uid()`) لإنشاء موعد، بينما نظام الحجز مصمم للمستخدمين المجهولين (الضيوف).

### 🚫 المشكلة السابقة:
```sql
CREATE POLICY "Clients can create appointments" ON public.appointments FOR INSERT WITH CHECK (
    client_id = auth.uid()
);
```

هذه السياسة تمنع المستخدمين غير المسجلين من إنشاء مواعيد لأن `auth.uid()` يعيد `null` للمستخدمين المجهولين.

## ✅ الحل المطبق

### 1. تعديل جدول المواعيد
- جعل `client_id` اختياري (nullable)
- إضافة حقول للمستخدمين المجهولين:
  - `guest_name` (اسم الضيف)
  - `guest_phone` (رقم هاتف الضيف)
  - `guest_email` (بريد إلكتروني اختياري)
  - `is_guest_booking` (علامة للمواعيد المجهولة)

### 2. سياسات RLS جديدة
- **سياسة للمواعيد المجهولة**: تسمح بإنشاء مواعيد بدون تسجيل دخول
- **سياسات منفصلة للمستخدمين المسجلين**: تحافظ على الأمان
- **سياسات للعمال والمشرفين**: تسمح بإدارة المواعيد

### 3. وظائف مساعدة
- `create_guest_appointment()`: إنشاء موعد مجهول بشكل آمن
- `get_guest_appointments()`: البحث عن مواعيد الضيوف برقم الهاتف

## 🚀 كيفية تطبيق الإصلاح

### الخطوة 1: تطبيق تحديث قاعدة البيانات
```bash
# في Supabase Dashboard أو عبر CLI
psql -h your-db-host -U postgres -d your-db-name -f supabase-rls-fix-appointments.sql
```

### الخطوة 2: التحقق من التطبيق
```sql
-- التحقق من الجدول المحدث
\d public.appointments

-- التحقق من السياسات الجديدة
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'appointments';

-- التحقق من الوظائف الجديدة
\df create_guest_appointment
\df get_guest_appointments
```

### الخطوة 3: اختبار النظام
```sql
-- اختبار إنشاء موعد مجهول
SELECT create_guest_appointment(
    'ليلى أحمد',                    -- p_guest_name
    '+963-987-123456',              -- p_guest_phone
    '2025-01-15',                   -- p_appointment_date
    '18:00',                        -- p_appointment_time
    '<EMAIL>',            -- p_guest_email (optional)
    'استشارة فستان زفاف',           -- p_service_type (optional)
    'أريد فستان زفاف كلاسيكي'       -- p_notes (optional)
);

-- اختبار البحث عن المواعيد
SELECT * FROM get_guest_appointments('+963-987-123456');
```

## 🔒 الأمان والحماية

### ما تم الحفاظ عليه:
- ✅ المستخدمون المسجلون يمكنهم رؤية مواعيدهم فقط
- ✅ العمال يمكنهم رؤية مواعيدهم المخصصة فقط
- ✅ المشرفون يمكنهم إدارة جميع المواعيد
- ✅ لا يمكن للمستخدمين المجهولين رؤية مواعيد الآخرين

### ما تم إضافته:
- ✅ المستخدمون المجهولون يمكنهم إنشاء مواعيد
- ✅ البحث عن المواعيد برقم الهاتف (للضيوف فقط)
- ✅ التحقق من توفر المواعيد قبل الحجز
- ✅ فهارس محسنة للأداء

## 📱 التحديثات في الكود

### 1. واجهات TypeScript محدثة
```typescript
// في src/lib/database-v2.ts
export interface Appointment {
  // ... الحقول الموجودة
  is_guest_booking: boolean
  guest_name?: string
  guest_phone?: string
  guest_email?: string
}

export interface GuestAppointmentData {
  guest_name: string
  guest_phone: string
  guest_email?: string
  appointment_date: string
  appointment_time: string
  service_type?: string
  notes?: string
}
```

### 2. خدمات محدثة
```typescript
// في src/lib/appointments.ts
static async bookGuestAppointment(appointmentData: GuestAppointmentData)
static async getGuestAppointments(phoneNumber: string)
```

### 3. مكونات جديدة
- `GuestAppointmentLookup`: للبحث عن المواعيد
- صفحة `/my-appointments`: لعرض مواعيد الضيوف

## 🧪 اختبار النظام

### 1. اختبار الحجز المجهول
```javascript
// في المتصفح أو Node.js
const result = await AppointmentService.bookGuestAppointment({
  client_name: 'نور محمد',
  client_phone: '+963-987-654321',
  client_email: '<EMAIL>',
  appointment_date: '2025-01-20',
  appointment_time: '19:00',
  service_type: 'consultation',
  notes: 'استشارة فستان سهرة'
});

console.log(result);
```

### 2. اختبار البحث عن المواعيد
```javascript
const appointments = await AppointmentService.getGuestAppointments('+963-987-654321');
console.log(appointments);
```

## 📊 البيانات التجريبية

تم إدخال بيانات تجريبية للاختبار:
- موعد لـ "ليلى أحمد" - استشارة فستان زفاف
- موعد لـ "نور محمد" - استشارة فستان سهرة

## 🔄 الخطوات التالية

1. **تطبيق التحديث**: تشغيل ملف SQL في قاعدة البيانات
2. **اختبار النظام**: التأكد من عمل الحجز المجهول
3. **تحديث الواجهة**: إضافة الحقول الجديدة للنموذج
4. **اختبار شامل**: التأكد من عمل جميع الوظائف
5. **نشر التحديث**: رفع التغييرات للإنتاج

## 🆘 استكشاف الأخطاء

### خطأ في إنشاء الموعد:
```sql
-- التحقق من السياسات
SELECT * FROM pg_policies WHERE tablename = 'appointments';

-- التحقق من الصلاحيات
SELECT has_table_privilege('anon', 'appointments', 'INSERT');
```

### خطأ في البحث:
```sql
-- التحقق من الوظيفة
SELECT * FROM get_guest_appointments('test-phone');
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من تطبيق ملف SQL بشكل صحيح
2. تأكد من تحديث الكود في التطبيق
3. اختبر الوظائف في قاعدة البيانات مباشرة
4. راجع سجلات الأخطاء في Supabase Dashboard

---

**✨ تم إصلاح المشكلة بنجاح! الآن يمكن للمستخدمين المجهولين حجز المواعيد بأمان.**
