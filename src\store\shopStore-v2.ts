// Enhanced Shop Store with Supabase Integration
// متجر المحل المحسن مع تكامل Supabase

'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { isSupabaseConfigured } from '@/lib/supabase'
import { 
  favoriteService, 
  cartService,
  type Favorite,
  type CartItem as DBCartItem
} from '@/lib/database-v2'

// Enhanced product interface
export interface Product {
  id: string
  name: string
  price: number
  image: string
  description?: string
  category?: string
  sizes?: string[]
  colors?: string[]
  // Additional fields from database
  name_en?: string
  description_en?: string
  sku?: string
  sale_price?: number
  stock_quantity?: number
  is_active?: boolean
}

// Enhanced cart item interface
export interface CartItem extends Product {
  quantity: number
  selectedSize?: string
  selectedColor?: string
  // Database fields
  cart_item_id?: string
  user_id?: string
  added_at?: string
}

interface ShopState {
  // Data
  favorites: Product[]
  cart: CartItem[]
  
  // Loading states
  isLoading: boolean
  isFavoritesLoading: boolean
  isCartLoading: boolean
  
  // Error states
  error: string | null
  favoritesError: string | null
  cartError: string | null

  // Favorites management
  loadFavorites: (userId?: string) => Promise<void>
  addToFavorites: (product: Product, userId?: string) => Promise<boolean>
  removeFromFavorites: (productId: string, userId?: string) => Promise<boolean>
  isFavorite: (productId: string) => boolean
  clearFavorites: (userId?: string) => Promise<void>

  // Cart management
  loadCart: (userId?: string) => Promise<void>
  addToCart: (product: Product, quantity?: number, size?: string, color?: string, userId?: string) => Promise<boolean>
  removeFromCart: (productId: string, userId?: string) => Promise<boolean>
  isInCart: (productId: string) => boolean
  updateCartItemQuantity: (productId: string, quantity: number, userId?: string) => Promise<boolean>
  clearCart: (userId?: string) => Promise<void>
  getCartTotal: () => number
  getCartItemsCount: () => number

  // Utility functions
  clearError: () => void
  clearAllErrors: () => void
  setLoading: (loading: boolean) => void
  
  // Sync functions for user login/logout
  syncUserData: (userId: string) => Promise<void>
  clearUserData: () => void
}

// Generate unique ID for fallback mode
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Mock data for fallback mode
const mockFavorites: Product[] = []
const mockCart: CartItem[] = []

export const useShopStore = create<ShopState>()(
  persist(
    (set, get) => ({
      // Initial state
      favorites: [],
      cart: [],
      isLoading: false,
      isFavoritesLoading: false,
      isCartLoading: false,
      error: null,
      favoritesError: null,
      cartError: null,

      // Favorites management
      loadFavorites: async (userId) => {
        set({ isFavoritesLoading: true, favoritesError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { favorites, error } = await favoriteService.getUserFavorites(userId)
            
            if (error) {
              set({ favoritesError: error.message, isFavoritesLoading: false })
              return
            }
            
            // Convert database favorites to products
            const products = favorites?.map(fav => fav.product).filter(Boolean) || []
            set({ favorites: products, isFavoritesLoading: false })
          } else {
            // Fallback to local storage
            await new Promise(resolve => setTimeout(resolve, 300))
            set({ favorites: [...mockFavorites], isFavoritesLoading: false })
          }
        } catch (error) {
          console.error('Load favorites error:', error)
          set({ favoritesError: 'خطأ في تحميل المفضلة', isFavoritesLoading: false })
        }
      },

      addToFavorites: async (product, userId) => {
        set({ isFavoritesLoading: true, favoritesError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { favorite, error } = await favoriteService.addToFavorites(userId, product.id)
            
            if (error) {
              set({ favoritesError: error.message, isFavoritesLoading: false })
              return false
            }
            
            // Add to local state
            set(state => ({
              favorites: [...state.favorites.filter(f => f.id !== product.id), product],
              isFavoritesLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 200))
            
            if (!mockFavorites.find(f => f.id === product.id)) {
              mockFavorites.push(product)
            }
            
            set(state => ({
              favorites: [...state.favorites.filter(f => f.id !== product.id), product],
              isFavoritesLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Add to favorites error:', error)
          set({ favoritesError: 'خطأ في إضافة المنتج للمفضلة', isFavoritesLoading: false })
          return false
        }
      },

      removeFromFavorites: async (productId, userId) => {
        set({ isFavoritesLoading: true, favoritesError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { error } = await favoriteService.removeFromFavorites(userId, productId)
            
            if (error) {
              set({ favoritesError: error.message, isFavoritesLoading: false })
              return false
            }
            
            // Remove from local state
            set(state => ({
              favorites: state.favorites.filter(f => f.id !== productId),
              isFavoritesLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 200))
            
            const index = mockFavorites.findIndex(f => f.id === productId)
            if (index !== -1) {
              mockFavorites.splice(index, 1)
            }
            
            set(state => ({
              favorites: state.favorites.filter(f => f.id !== productId),
              isFavoritesLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Remove from favorites error:', error)
          set({ favoritesError: 'خطأ في إزالة المنتج من المفضلة', isFavoritesLoading: false })
          return false
        }
      },

      isFavorite: (productId) => {
        return get().favorites.some(item => item.id === productId)
      },

      clearFavorites: async (userId) => {
        set({ isFavoritesLoading: true, favoritesError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { error } = await favoriteService.clearUserFavorites(userId)
            
            if (error) {
              set({ favoritesError: error.message, isFavoritesLoading: false })
              return
            }
          } else {
            // Fallback mode
            mockFavorites.length = 0
          }
          
          set({ favorites: [], isFavoritesLoading: false })
        } catch (error) {
          console.error('Clear favorites error:', error)
          set({ favoritesError: 'خطأ في مسح المفضلة', isFavoritesLoading: false })
        }
      },

      // Cart management
      loadCart: async (userId) => {
        set({ isCartLoading: true, cartError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { cartItems, error } = await cartService.getUserCart(userId)
            
            if (error) {
              set({ cartError: error.message, isCartLoading: false })
              return
            }
            
            // Convert database cart items to local format
            const items: CartItem[] = cartItems?.map(item => ({
              ...item.product,
              quantity: item.quantity,
              selectedSize: item.selected_size || undefined,
              selectedColor: item.selected_color || undefined,
              cart_item_id: item.id,
              user_id: item.user_id,
              added_at: item.created_at
            })).filter(Boolean) || []
            
            set({ cart: items, isCartLoading: false })
          } else {
            // Fallback to local storage
            await new Promise(resolve => setTimeout(resolve, 300))
            set({ cart: [...mockCart], isCartLoading: false })
          }
        } catch (error) {
          console.error('Load cart error:', error)
          set({ cartError: 'خطأ في تحميل السلة', isCartLoading: false })
        }
      },

      addToCart: async (product, quantity = 1, size, color, userId) => {
        set({ isCartLoading: true, cartError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { cartItem, error } = await cartService.addToCart(
              userId, 
              product.id, 
              quantity, 
              size, 
              color
            )
            
            if (error) {
              set({ cartError: error.message, isCartLoading: false })
              return false
            }
            
            // Update local state
            const newItem: CartItem = {
              ...product,
              quantity,
              selectedSize: size,
              selectedColor: color,
              cart_item_id: cartItem?.id,
              user_id: userId,
              added_at: new Date().toISOString()
            }
            
            set(state => {
              const existingIndex = state.cart.findIndex(item => 
                item.id === product.id && 
                item.selectedSize === size && 
                item.selectedColor === color
              )
              
              if (existingIndex !== -1) {
                // Update existing item
                const updatedCart = [...state.cart]
                updatedCart[existingIndex] = {
                  ...updatedCart[existingIndex],
                  quantity: updatedCart[existingIndex].quantity + quantity
                }
                return { cart: updatedCart, isCartLoading: false }
              } else {
                // Add new item
                return { cart: [...state.cart, newItem], isCartLoading: false }
              }
            })
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 200))
            
            const existingIndex = mockCart.findIndex(item => 
              item.id === product.id && 
              item.selectedSize === size && 
              item.selectedColor === color
            )
            
            if (existingIndex !== -1) {
              mockCart[existingIndex].quantity += quantity
            } else {
              const newItem: CartItem = {
                ...product,
                quantity,
                selectedSize: size,
                selectedColor: color
              }
              mockCart.push(newItem)
            }
            
            set(state => {
              const existingIndex = state.cart.findIndex(item => 
                item.id === product.id && 
                item.selectedSize === size && 
                item.selectedColor === color
              )
              
              if (existingIndex !== -1) {
                const updatedCart = [...state.cart]
                updatedCart[existingIndex] = {
                  ...updatedCart[existingIndex],
                  quantity: updatedCart[existingIndex].quantity + quantity
                }
                return { cart: updatedCart, isCartLoading: false }
              } else {
                const newItem: CartItem = {
                  ...product,
                  quantity,
                  selectedSize: size,
                  selectedColor: color
                }
                return { cart: [...state.cart, newItem], isCartLoading: false }
              }
            })
            
            return true
          }
        } catch (error) {
          console.error('Add to cart error:', error)
          set({ cartError: 'خطأ في إضافة المنتج للسلة', isCartLoading: false })
          return false
        }
      },

      removeFromCart: async (productId, userId) => {
        set({ isCartLoading: true, cartError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { error } = await cartService.removeFromCart(userId, productId)
            
            if (error) {
              set({ cartError: error.message, isCartLoading: false })
              return false
            }
            
            // Remove from local state
            set(state => ({
              cart: state.cart.filter(item => item.id !== productId),
              isCartLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 200))
            
            const index = mockCart.findIndex(item => item.id === productId)
            if (index !== -1) {
              mockCart.splice(index, 1)
            }
            
            set(state => ({
              cart: state.cart.filter(item => item.id !== productId),
              isCartLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Remove from cart error:', error)
          set({ cartError: 'خطأ في إزالة المنتج من السلة', isCartLoading: false })
          return false
        }
      },

      isInCart: (productId) => {
        return get().cart.some(item => item.id === productId)
      },

      updateCartItemQuantity: async (productId, quantity, userId) => {
        if (quantity <= 0) {
          return get().removeFromCart(productId, userId)
        }
        
        set({ isCartLoading: true, cartError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { error } = await cartService.updateCartItemQuantity(userId, productId, quantity)
            
            if (error) {
              set({ cartError: error.message, isCartLoading: false })
              return false
            }
            
            // Update local state
            set(state => ({
              cart: state.cart.map(item =>
                item.id === productId ? { ...item, quantity } : item
              ),
              isCartLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 200))
            
            const item = mockCart.find(item => item.id === productId)
            if (item) {
              item.quantity = quantity
            }
            
            set(state => ({
              cart: state.cart.map(item =>
                item.id === productId ? { ...item, quantity } : item
              ),
              isCartLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Update cart quantity error:', error)
          set({ cartError: 'خطأ في تحديث كمية المنتج', isCartLoading: false })
          return false
        }
      },

      clearCart: async (userId) => {
        set({ isCartLoading: true, cartError: null })
        
        try {
          if (isSupabaseConfigured() && userId) {
            const { error } = await cartService.clearUserCart(userId)
            
            if (error) {
              set({ cartError: error.message, isCartLoading: false })
              return
            }
          } else {
            // Fallback mode
            mockCart.length = 0
          }
          
          set({ cart: [], isCartLoading: false })
        } catch (error) {
          console.error('Clear cart error:', error)
          set({ cartError: 'خطأ في مسح السلة', isCartLoading: false })
        }
      },

      getCartTotal: () => {
        const { cart } = get()
        return cart.reduce((total, item) => {
          const price = item.sale_price || item.price
          return total + (price * item.quantity)
        }, 0)
      },

      getCartItemsCount: () => {
        const { cart } = get()
        return cart.reduce((total, item) => total + item.quantity, 0)
      },

      // Utility functions
      clearError: () => {
        set({ error: null })
      },

      clearAllErrors: () => {
        set({ 
          error: null, 
          favoritesError: null, 
          cartError: null 
        })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      // Sync functions for user login/logout
      syncUserData: async (userId) => {
        const state = get()
        await Promise.all([
          state.loadFavorites(userId),
          state.loadCart(userId)
        ])
      },

      clearUserData: () => {
        set({ 
          favorites: [], 
          cart: [],
          favoritesError: null,
          cartError: null
        })
      }
    }),
    {
      name: 'yasmin-alsham-shop-v2',
      partialize: (state) => ({
        // Only persist data when in fallback mode (no user logged in)
        favorites: isSupabaseConfigured() ? [] : state.favorites,
        cart: isSupabaseConfigured() ? [] : state.cart
      })
    }
  )
)

// Helper function to format price
export const formatPrice = (price: number): string => {
  return `${price.toLocaleString('en')} ريال`
}

// Migration utility to transfer data from old store
export const migrateFromOldStore = () => {
  try {
    // Get data from old localStorage keys
    const oldShopData = localStorage.getItem('yasmin-alsham-shop')

    if (oldShopData) {
      const parsed = JSON.parse(oldShopData)
      const state = parsed?.state

      if (state) {
        // Get current store instance
        const shopStore = useShopStore.getState()

        // Migrate favorites
        if (state.favorites && Array.isArray(state.favorites)) {
          state.favorites.forEach((product: Product) => {
            if (!shopStore.isFavorite(product.id)) {
              mockFavorites.push(product)
            }
          })
        }

        // Migrate cart
        if (state.cart && Array.isArray(state.cart)) {
          state.cart.forEach((item: CartItem) => {
            const existingIndex = mockCart.findIndex(cartItem =>
              cartItem.id === item.id &&
              cartItem.selectedSize === item.selectedSize &&
              cartItem.selectedColor === item.selectedColor
            )

            if (existingIndex === -1) {
              mockCart.push(item)
            }
          })
        }

        // Update store state
        shopStore.favorites = [...mockFavorites]
        shopStore.cart = [...mockCart]

        console.log('Successfully migrated shop data from old store')

        // Optionally remove old data
        // localStorage.removeItem('yasmin-alsham-shop')
      }
    }
  } catch (error) {
    console.error('Error migrating shop data:', error)
  }
}
