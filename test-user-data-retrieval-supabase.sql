-- اختبار استرجاع بيانات المستخدمين - متوافق مع Supabase SQL Editor
-- Test User Data Retrieval - Supabase SQL Editor Compatible
-- Yasmin Al-Sham Project

-- ========================================
-- اختبارات شاملة لاسترجاع بيانات المستخدمين
-- ========================================

SELECT '🧪 بدء الاختبارات الشاملة لاسترجاع بيانات المستخدمين' as test_start_title;

-- ========================================
-- اختبار 1: فحص البنية الأساسية
-- ========================================

SELECT '📋 اختبار 1: فحص البنية الأساسية' as test1_title;

-- فحص وجود الجداول
SELECT 
    'وجود الجداول:' as check_name,
    EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') as users_table_exists,
    EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') as user_roles_table_exists;

-- فحص حالة RLS
SELECT 
    'حالة RLS:' as check_name,
    (SELECT rowsecurity FROM pg_tables WHERE tablename = 'users' AND schemaname = 'public') as users_rls_enabled,
    COALESCE((SELECT rowsecurity FROM pg_tables WHERE tablename = 'user_roles' AND schemaname = 'public'), false) as user_roles_rls_enabled;

-- فحص عدد السياسات
SELECT 
    'عدد السياسات:' as check_name,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'users' AND schemaname = 'public') as users_policies_count,
    COALESCE((SELECT COUNT(*) FROM pg_policies WHERE tablename = 'user_roles' AND schemaname = 'public'), 0) as user_roles_policies_count;

-- ========================================
-- اختبار 2: فحص البيانات الموجودة
-- ========================================

SELECT '📊 اختبار 2: فحص البيانات الموجودة' as test2_title;

-- إحصائيات جدول المستخدمين (من منظور المشرف)
SELECT 
    'إحصائيات المستخدمين:' as stats_name,
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_count,
    COUNT(*) FILTER (WHERE role = 'client') as client_count,
    COUNT(*) FILTER (WHERE role = 'worker') as worker_count,
    COUNT(*) FILTER (WHERE is_active = true) as active_users
FROM public.users;

-- إحصائيات جدول الأدوار (إذا كان موجوداً)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        RAISE NOTICE '📊 إحصائيات جدول الأدوار:';
        PERFORM (
            SELECT 
                COUNT(*) as total_roles,
                COUNT(*) FILTER (WHERE role = 'admin') as admin_roles,
                COUNT(*) FILTER (WHERE role = 'client') as client_roles,
                COUNT(*) FILTER (WHERE role = 'worker') as worker_roles
            FROM public.user_roles
        );
    ELSE
        RAISE NOTICE '⚠️ جدول الأدوار غير موجود';
    END IF;
END $$;

-- ========================================
-- اختبار 3: اختبار الوظائف المساعدة
-- ========================================

SELECT '🔧 اختبار 3: اختبار الوظائف المساعدة' as test3_title;

-- فحص وجود الوظائف المساعدة
SELECT 
    'الوظائف المساعدة الموجودة:' as functions_check,
    EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_user_role' AND routine_schema = 'public') as get_user_role_exists,
    EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'is_admin' AND routine_schema = 'public') as is_admin_exists,
    EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_current_user_data' AND routine_schema = 'public') as get_current_user_data_exists,
    EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_all_users' AND routine_schema = 'public') as get_all_users_exists;

-- اختبار الوظائف المساعدة مع معالجة الأخطاء
DO $$
DECLARE
    user_role_result VARCHAR(20);
    admin_result BOOLEAN;
    user_data_count INTEGER;
    all_users_count INTEGER;
    error_message TEXT;
BEGIN
    -- اختبار get_user_role
    BEGIN
        SELECT get_user_role() INTO user_role_result;
        RAISE NOTICE '✅ get_user_role: %', COALESCE(user_role_result, 'NULL');
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في get_user_role: %', error_message;
    END;
    
    -- اختبار is_admin
    BEGIN
        SELECT is_admin() INTO admin_result;
        RAISE NOTICE '✅ is_admin: %', COALESCE(admin_result::TEXT, 'NULL');
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في is_admin: %', error_message;
    END;
    
    -- اختبار get_current_user_data
    BEGIN
        SELECT COUNT(*) INTO user_data_count FROM get_current_user_data();
        RAISE NOTICE '✅ get_current_user_data: % سجل', user_data_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في get_current_user_data: %', error_message;
    END;
    
    -- اختبار get_all_users
    BEGIN
        SELECT COUNT(*) INTO all_users_count FROM get_all_users();
        RAISE NOTICE '✅ get_all_users: % مستخدم', all_users_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في get_all_users: %', error_message;
    END;
END $$;

-- ========================================
-- اختبار 4: اختبار الوصول المباشر للجداول
-- ========================================

SELECT '🔍 اختبار 4: اختبار الوصول المباشر للجداول' as test4_title;

-- اختبار قراءة جدول المستخدمين
DO $$
DECLARE
    users_count INTEGER;
    error_message TEXT;
BEGIN
    BEGIN
        SELECT COUNT(*) INTO users_count FROM public.users;
        RAISE NOTICE '✅ قراءة جدول المستخدمين: % مستخدم مرئي', users_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في قراءة جدول المستخدمين: %', error_message;
    END;
END $$;

-- اختبار قراءة جدول الأدوار (إذا كان موجوداً)
DO $$
DECLARE
    roles_count INTEGER;
    error_message TEXT;
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        BEGIN
            SELECT COUNT(*) INTO roles_count FROM public.user_roles;
            RAISE NOTICE '✅ قراءة جدول الأدوار: % دور مرئي', roles_count;
        EXCEPTION WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
            RAISE NOTICE '❌ خطأ في قراءة جدول الأدوار: %', error_message;
        END;
    ELSE
        RAISE NOTICE '⚠️ جدول الأدوار غير موجود - تخطي الاختبار';
    END IF;
END $$;

-- ========================================
-- اختبار 5: اختبار السيناريوهات المختلفة
-- ========================================

SELECT '🎭 اختبار 5: اختبار السيناريوهات المختلفة' as test5_title;

-- سيناريو 1: مستخدم غير مسجل دخول (SQL Editor)
SELECT 
    'سيناريو 1 - غير مسجل دخول:' as scenario,
    auth.uid() as auth_uid,
    CASE 
        WHEN auth.uid() IS NULL THEN '✅ متوقع (SQL Editor)'
        ELSE '⚠️ غير متوقع'
    END as scenario_status;

-- سيناريو 2: محاولة الوصول للمستخدمين
SELECT 
    'سيناريو 2 - الوصول للمستخدمين:' as scenario,
    COUNT(*) as accessible_users,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ يمكن الوصول'
        ELSE '❌ لا يمكن الوصول'
    END as access_status
FROM public.users;

-- ========================================
-- اختبار 6: اختبار المشرفين المتاحين
-- ========================================

SELECT '👑 اختبار 6: اختبار المشرفين المتاحين' as test6_title;

-- عرض المشرفين المتاحين
SELECT 
    'المشرفين المتاحين:' as admins_title,
    u.id,
    u.email,
    u.full_name,
    u.role,
    u.is_active,
    u.created_at
FROM public.users u
WHERE u.role = 'admin'
ORDER BY u.created_at;

-- ========================================
-- اختبار 7: اختبار التزامن بين الجداول
-- ========================================

SELECT '🔄 اختبار 7: اختبار التزامن بين الجداول' as test7_title;

-- فحص التزامن بين جدول المستخدمين وجدول الأدوار (إذا كان موجوداً)
DO $$
DECLARE
    users_count INTEGER;
    roles_count INTEGER;
    orphaned_users INTEGER;
    orphaned_roles INTEGER;
BEGIN
    SELECT COUNT(*) INTO users_count FROM public.users;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        SELECT COUNT(*) INTO roles_count FROM public.user_roles;
        
        -- فحص المستخدمين بدون أدوار
        SELECT COUNT(*) INTO orphaned_users
        FROM public.users u
        LEFT JOIN public.user_roles ur ON u.id = ur.user_id
        WHERE ur.user_id IS NULL;
        
        -- فحص أدوار بدون مستخدمين
        SELECT COUNT(*) INTO orphaned_roles
        FROM public.user_roles ur
        LEFT JOIN public.users u ON ur.user_id = u.id
        WHERE u.id IS NULL;
        
        RAISE NOTICE '📊 إحصائيات التزامن:';
        RAISE NOTICE '   المستخدمين: %', users_count;
        RAISE NOTICE '   الأدوار: %', roles_count;
        RAISE NOTICE '   مستخدمين بدون أدوار: %', orphaned_users;
        RAISE NOTICE '   أدوار بدون مستخدمين: %', orphaned_roles;
        
        IF users_count = roles_count AND orphaned_users = 0 AND orphaned_roles = 0 THEN
            RAISE NOTICE '✅ الجداول متزامنة بشكل صحيح';
        ELSE
            RAISE NOTICE '⚠️ يوجد عدم تزامن بين الجداول';
        END IF;
    ELSE
        RAISE NOTICE '⚠️ جدول الأدوار غير موجود - تخطي فحص التزامن';
    END IF;
END $$;

-- ========================================
-- اختبار 8: اختبار الأداء (متوافق مع Supabase)
-- ========================================

SELECT '⚡ اختبار 8: اختبار الأداء' as test8_title;

-- قياس وقت تنفيذ الاستعلامات باستخدام clock_timestamp()
DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
    users_count INTEGER;
    roles_count INTEGER;
    join_count INTEGER;
BEGIN
    -- اختبار استعلام المستخدمين
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO users_count FROM public.users;
    end_time := clock_timestamp();
    duration := end_time - start_time;
    RAISE NOTICE '⏱️ استعلام المستخدمين: % مستخدم في %', users_count, duration;
    
    -- اختبار استعلام الأدوار (إذا كان الجدول موجوداً)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        start_time := clock_timestamp();
        SELECT COUNT(*) INTO roles_count FROM public.user_roles;
        end_time := clock_timestamp();
        duration := end_time - start_time;
        RAISE NOTICE '⏱️ استعلام الأدوار: % دور في %', roles_count, duration;
        
        -- اختبار استعلام مع JOIN
        start_time := clock_timestamp();
        SELECT COUNT(*) INTO join_count 
        FROM public.users u JOIN public.user_roles ur ON u.id = ur.user_id;
        end_time := clock_timestamp();
        duration := end_time - start_time;
        RAISE NOTICE '⏱️ استعلام مع JOIN: % سجل في %', join_count, duration;
    ELSE
        RAISE NOTICE '⚠️ جدول الأدوار غير موجود - تخطي اختبارات الأداء المتقدمة';
    END IF;
END $$;

-- عرض نتائج الأداء في جدول
DO $$
DECLARE
    users_count INTEGER;
    roles_count INTEGER := 0;
BEGIN
    SELECT COUNT(*) INTO users_count FROM public.users;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        SELECT COUNT(*) INTO roles_count FROM public.user_roles;
    END IF;

    RAISE NOTICE '📊 نتائج اختبار الأداء:';
    RAISE NOTICE '   سجلات جدول المستخدمين: %', users_count;
    RAISE NOTICE '   سجلات جدول الأدوار: %', roles_count;
END $$;

-- ========================================
-- ملخص نتائج الاختبار
-- ========================================

SELECT '========================================' as separator;
SELECT '📋 ملخص نتائج اختبار استرجاع بيانات المستخدمين' as summary_title;
SELECT '========================================' as separator;

-- ملخص شامل
SELECT 
    'نتائج الاختبار:' as test_results,
    'المستخدمين: ' || (SELECT COUNT(*) FROM public.users) as users_accessible,
    'المشرفين: ' || (SELECT COUNT(*) FROM public.users WHERE role = 'admin') as admins_available,
    'الوظائف: ' || (
        SELECT COUNT(*) FROM information_schema.routines 
        WHERE routine_schema = 'public' 
        AND routine_name IN ('get_user_role', 'is_admin', 'get_current_user_data', 'get_all_users')
    ) as helper_functions;

-- حالة النظام
SELECT 
    'حالة النظام:' as system_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.users) > 0 AND 
             (SELECT COUNT(*) FROM public.users WHERE role = 'admin') > 0
        THEN '✅ النظام يعمل بشكل صحيح'
        ELSE '❌ يحتاج إلى إصلاح'
    END as overall_status;

-- ========================================
-- توصيات للخطوات التالية
-- ========================================

SELECT '📝 توصيات للخطوات التالية' as recommendations_title;

SELECT '1. إذا كانت النتائج إيجابية، جرب الاستعلامات في التطبيق' as recommendation1;
SELECT '2. استخدم الوظائف المساعدة بدلاً من الاستعلامات المباشرة' as recommendation2;
SELECT '3. تأكد من تسجيل الدخول كمشرف في التطبيق' as recommendation3;
SELECT '4. اختبر الوظائف: get_all_users(), get_current_user_data(), is_admin()' as recommendation4;

SELECT '🎉 اختبار استرجاع بيانات المستخدمين مكتمل!' as test_completion_message;
