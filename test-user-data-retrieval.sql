-- اختبار استرجاع بيانات المستخدمين
-- Test User Data Retrieval
-- Ya<PERSON>in Al-Sham Project

-- ========================================
-- اختبارات شاملة لاسترجاع بيانات المستخدمين
-- ========================================

SELECT '🧪 بدء الاختبارات الشاملة لاسترجاع بيانات المستخدمين' as test_start_title;

-- ========================================
-- اختبار 1: فحص البنية الأساسية
-- ========================================

SELECT '📋 اختبار 1: فحص البنية الأساسية' as test1_title;

-- فحص وجود الجداول
SELECT 
    'وجود الجداول:' as check_name,
    EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') as users_table_exists,
    EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') as user_roles_table_exists;

-- فحص حالة RLS
SELECT 
    'حالة RLS:' as check_name,
    (SELECT rowsecurity FROM pg_tables WHERE tablename = 'users' AND schemaname = 'public') as users_rls_enabled,
    (SELECT rowsecurity FROM pg_tables WHERE tablename = 'user_roles' AND schemaname = 'public') as user_roles_rls_enabled;

-- فحص عدد السياسات
SELECT 
    'عدد السياسات:' as check_name,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'users' AND schemaname = 'public') as users_policies_count,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'user_roles' AND schemaname = 'public') as user_roles_policies_count;

-- ========================================
-- اختبار 2: فحص البيانات الموجودة
-- ========================================

SELECT '📊 اختبار 2: فحص البيانات الموجودة' as test2_title;

-- إحصائيات جدول المستخدمين (من منظور المشرف)
SELECT 
    'إحصائيات المستخدمين:' as stats_name,
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_count,
    COUNT(*) FILTER (WHERE role = 'client') as client_count,
    COUNT(*) FILTER (WHERE role = 'worker') as worker_count,
    COUNT(*) FILTER (WHERE is_active = true) as active_users
FROM public.users;

-- إحصائيات جدول الأدوار
SELECT 
    'إحصائيات الأدوار:' as roles_stats_name,
    COUNT(*) as total_roles,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_roles,
    COUNT(*) FILTER (WHERE role = 'client') as client_roles,
    COUNT(*) FILTER (WHERE role = 'worker') as worker_roles
FROM public.user_roles;

-- ========================================
-- اختبار 3: اختبار الوظائف المساعدة
-- ========================================

SELECT '🔧 اختبار 3: اختبار الوظائف المساعدة' as test3_title;

-- اختبار وظيفة get_user_role
SELECT 
    'اختبار get_user_role:' as function_test,
    get_user_role() as current_user_role,
    CASE 
        WHEN get_user_role() IS NOT NULL THEN '✅ تعمل'
        ELSE '❌ لا تعمل'
    END as function_status;

-- اختبار وظيفة is_admin
SELECT 
    'اختبار is_admin:' as function_test,
    is_admin() as current_user_is_admin,
    CASE 
        WHEN is_admin() IS NOT NULL THEN '✅ تعمل'
        ELSE '❌ لا تعمل'
    END as function_status;

-- اختبار وظيفة get_user_profile
SELECT 
    'اختبار get_user_profile:' as function_test,
    COUNT(*) as profile_records_returned
FROM get_user_profile();

-- اختبار وظيفة get_all_users
SELECT 
    'اختبار get_all_users:' as function_test,
    COUNT(*) as all_users_returned
FROM get_all_users();

-- ========================================
-- اختبار 4: اختبار الوصول المباشر للجداول
-- ========================================

SELECT '🔍 اختبار 4: اختبار الوصول المباشر للجداول' as test4_title;

-- اختبار قراءة جدول المستخدمين
DO $$
DECLARE
    users_count INTEGER;
    error_message TEXT;
BEGIN
    BEGIN
        SELECT COUNT(*) INTO users_count FROM public.users;
        RAISE NOTICE '✅ قراءة جدول المستخدمين: % مستخدم مرئي', users_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في قراءة جدول المستخدمين: %', error_message;
    END;
END $$;

-- اختبار قراءة جدول الأدوار
DO $$
DECLARE
    roles_count INTEGER;
    error_message TEXT;
BEGIN
    BEGIN
        SELECT COUNT(*) INTO roles_count FROM public.user_roles;
        RAISE NOTICE '✅ قراءة جدول الأدوار: % دور مرئي', roles_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '❌ خطأ في قراءة جدول الأدوار: %', error_message;
    END;
END $$;

-- ========================================
-- اختبار 5: اختبار السيناريوهات المختلفة
-- ========================================

SELECT '🎭 اختبار 5: اختبار السيناريوهات المختلفة' as test5_title;

-- سيناريو 1: مستخدم غير مسجل دخول (SQL Editor)
SELECT 
    'سيناريو 1 - غير مسجل دخول:' as scenario,
    auth.uid() as auth_uid,
    CASE 
        WHEN auth.uid() IS NULL THEN '✅ متوقع (SQL Editor)'
        ELSE '⚠️ غير متوقع'
    END as scenario_status;

-- سيناريو 2: محاولة الوصول للمستخدمين
SELECT 
    'سيناريو 2 - الوصول للمستخدمين:' as scenario,
    COUNT(*) as accessible_users,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ يمكن الوصول'
        ELSE '❌ لا يمكن الوصول'
    END as access_status
FROM public.users;

-- سيناريو 3: محاولة الوصول للأدوار
SELECT 
    'سيناريو 3 - الوصول للأدوار:' as scenario,
    COUNT(*) as accessible_roles,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ يمكن الوصول'
        ELSE '❌ لا يمكن الوصول'
    END as access_status
FROM public.user_roles;

-- ========================================
-- اختبار 6: اختبار المشرفين المتاحين
-- ========================================

SELECT '👑 اختبار 6: اختبار المشرفين المتاحين' as test6_title;

-- عرض المشرفين المتاحين
SELECT 
    'المشرفين المتاحين:' as admins_title,
    u.id,
    u.email,
    u.full_name,
    ur.role,
    u.is_active,
    u.created_at
FROM public.users u
JOIN public.user_roles ur ON u.id = ur.user_id
WHERE ur.role = 'admin'
ORDER BY u.created_at;

-- ========================================
-- اختبار 7: اختبار التزامن بين الجداول
-- ========================================

SELECT '🔄 اختبار 7: اختبار التزامن بين الجداول' as test7_title;

-- فحص التزامن بين جدول المستخدمين وجدول الأدوار
SELECT 
    'فحص التزامن:' as sync_check,
    (SELECT COUNT(*) FROM public.users) as users_count,
    (SELECT COUNT(*) FROM public.user_roles) as roles_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.users) = (SELECT COUNT(*) FROM public.user_roles) 
        THEN '✅ متزامن'
        ELSE '❌ غير متزامن'
    END as sync_status;

-- فحص المستخدمين بدون أدوار
SELECT 
    'مستخدمين بدون أدوار:' as orphaned_users,
    COUNT(*) as count
FROM public.users u
LEFT JOIN public.user_roles ur ON u.id = ur.user_id
WHERE ur.user_id IS NULL;

-- فحص أدوار بدون مستخدمين
SELECT 
    'أدوار بدون مستخدمين:' as orphaned_roles,
    COUNT(*) as count
FROM public.user_roles ur
LEFT JOIN public.users u ON ur.user_id = u.id
WHERE u.id IS NULL;

-- ========================================
-- اختبار 8: اختبار الأداء
-- ========================================

SELECT '⚡ اختبار 8: اختبار الأداء' as test8_title;

-- قياس وقت تنفيذ الاستعلامات (متوافق مع Supabase SQL Editor)
DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
    users_count INTEGER;
    roles_count INTEGER;
    join_count INTEGER;
BEGIN
    -- اختبار استعلام المستخدمين
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO users_count FROM public.users;
    end_time := clock_timestamp();
    duration := end_time - start_time;
    RAISE NOTICE '⏱️ استعلام المستخدمين: % مستخدم في %', users_count, duration;

    -- اختبار استعلام الأدوار
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO roles_count FROM public.user_roles;
    end_time := clock_timestamp();
    duration := end_time - start_time;
    RAISE NOTICE '⏱️ استعلام الأدوار: % دور في %', roles_count, duration;

    -- اختبار استعلام مع JOIN
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO join_count
    FROM public.users u JOIN public.user_roles ur ON u.id = ur.user_id;
    end_time := clock_timestamp();
    duration := end_time - start_time;
    RAISE NOTICE '⏱️ استعلام مع JOIN: % سجل في %', join_count, duration;
END $$;

-- عرض نتائج الأداء في جدول
SELECT
    'نتائج اختبار الأداء:' as performance_results,
    (SELECT COUNT(*) FROM public.users) as users_table_records,
    (SELECT COUNT(*) FROM public.user_roles) as roles_table_records,
    (SELECT COUNT(*) FROM public.users u JOIN public.user_roles ur ON u.id = ur.user_id) as joined_records;

-- ========================================
-- ملخص نتائج الاختبار
-- ========================================

SELECT '========================================' as separator;
SELECT '📋 ملخص نتائج اختبار استرجاع بيانات المستخدمين' as summary_title;
SELECT '========================================' as separator;

-- ملخص شامل
SELECT 
    'نتائج الاختبار:' as test_results,
    'المستخدمين: ' || (SELECT COUNT(*) FROM public.users) as users_accessible,
    'الأدوار: ' || (SELECT COUNT(*) FROM public.user_roles) as roles_accessible,
    'المشرفين: ' || (SELECT COUNT(*) FROM public.user_roles WHERE role = 'admin') as admins_available,
    'الوظائف: ' || (
        SELECT COUNT(*) FROM information_schema.routines 
        WHERE routine_schema = 'public' 
        AND routine_name IN ('get_user_role', 'is_admin', 'get_user_profile', 'get_all_users')
    ) as helper_functions;

-- حالة النظام
SELECT 
    'حالة النظام:' as system_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.users) > 0 AND 
             (SELECT COUNT(*) FROM public.user_roles) > 0 AND
             (SELECT COUNT(*) FROM public.user_roles WHERE role = 'admin') > 0
        THEN '✅ النظام يعمل بشكل صحيح'
        ELSE '❌ يحتاج إلى إصلاح'
    END as overall_status;

-- ========================================
-- توصيات للخطوات التالية
-- ========================================

SELECT '📝 توصيات للخطوات التالية' as recommendations_title;

SELECT '1. إذا كانت النتائج إيجابية، جرب الاستعلامات في التطبيق' as recommendation1;
SELECT '2. استخدم الوظائف المساعدة بدلاً من الاستعلامات المباشرة' as recommendation2;
SELECT '3. تأكد من تسجيل الدخول كمشرف في التطبيق' as recommendation3;
SELECT '4. اختبر الوظائف: get_all_users(), get_user_profile(), is_admin()' as recommendation4;

SELECT '🎉 اختبار استرجاع بيانات المستخدمين مكتمل!' as test_completion_message;
