-- إصلاح إنشاء العمال في قاعدة البيانات
-- Fix Worker Creation in Database
-- Ya<PERSON>in Al-Sham Project

-- ========================================
-- تشخيص المشكلة الحالية
-- ========================================

SELECT '🔍 تشخيص مشكلة إنشاء العمال' as diagnosis_title;

-- فحص السياسات الحالية للمستخدمين
SELECT 
    'سياسات المستخدمين الحالية:' as current_policies_title,
    COUNT(*) as total_policies
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public';

-- عرض السياسات الموجودة
SELECT 
    policyname as policy_name,
    cmd as command_type,
    CASE 
        WHEN cmd = 'INSERT' THEN '✅ مهم لإنشاء العمال'
        ELSE '📋 سياسة أخرى'
    END as relevance
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public'
ORDER BY cmd;

-- فحص وجود جدول العمال
SELECT 
    'جدول العمال:' as workers_table_check,
    EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'workers' AND table_schema = 'public') as table_exists;

-- ========================================
-- إنشاء وظيفة إنشاء العامل
-- ========================================

SELECT '🔧 إنشاء وظيفة إنشاء العامل' as create_function_title;

-- وظيفة شاملة لإنشاء عامل جديد
CREATE OR REPLACE FUNCTION create_worker(
    -- بيانات المستخدم الأساسية (مطلوبة)
    p_email VARCHAR(255),
    p_full_name VARCHAR(255),
    p_specialty VARCHAR(100),
    -- بيانات اختيارية (جميعها لها قيم افتراضية)
    p_phone VARCHAR(20) DEFAULT NULL,
    p_experience_years INTEGER DEFAULT 0,
    p_hourly_rate DECIMAL(10,2) DEFAULT NULL,
    p_skills TEXT[] DEFAULT NULL,
    p_bio TEXT DEFAULT NULL,
    p_is_active BOOLEAN DEFAULT true,
    p_is_available BOOLEAN DEFAULT true
) RETURNS UUID AS $$
DECLARE
    new_user_id UUID;
    new_worker_id UUID;
    admin_count INTEGER;
BEGIN
    -- التحقق من وجود مشرف في النظام أو أن المستخدم الحالي مشرف
    SELECT COUNT(*) INTO admin_count FROM public.users WHERE role = 'admin';
    
    IF admin_count = 0 THEN
        -- السماح بإنشاء أول عامل إذا لم يكن هناك مشرفين (للإعداد الأولي)
        RAISE NOTICE 'إنشاء عامل في الإعداد الأولي (لا يوجد مشرفين)';
    ELSIF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'يجب تسجيل الدخول لإنشاء عامل';
    ELSIF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
        RAISE EXCEPTION 'فقط المشرفين يمكنهم إنشاء عمال جدد';
    END IF;
    
    -- التحقق من عدم وجود البريد الإلكتروني مسبقاً
    IF EXISTS (SELECT 1 FROM public.users WHERE email = p_email) THEN
        RAISE EXCEPTION 'البريد الإلكتروني موجود مسبقاً: %', p_email;
    END IF;
    
    -- إنشاء المستخدم في جدول المستخدمين
    INSERT INTO public.users (
        email, 
        full_name, 
        phone, 
        role, 
        is_active,
        email_verified,
        created_at,
        updated_at
    ) VALUES (
        p_email,
        p_full_name,
        p_phone,
        'worker',
        p_is_active,
        false, -- سيتم التحقق من البريد لاحقاً
        NOW(),
        NOW()
    ) RETURNING id INTO new_user_id;
    
    -- إنشاء سجل العامل في جدول العمال
    INSERT INTO public.workers (
        user_id,
        specialty,
        experience_years,
        hourly_rate,
        performance_rating,
        total_completed_orders,
        skills,
        bio,
        is_available,
        created_at,
        updated_at
    ) VALUES (
        new_user_id,
        p_specialty,
        p_experience_years,
        p_hourly_rate,
        0.00, -- تقييم ابتدائي
        0, -- عدد الطلبات المكتملة
        COALESCE(p_skills, ARRAY[]::TEXT[]),
        p_bio,
        p_is_available,
        NOW(),
        NOW()
    ) RETURNING id INTO new_worker_id;
    
    -- إنشاء سجل في جدول الأدوار (إذا كان موجوداً)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_roles' AND table_schema = 'public') THEN
        INSERT INTO public.user_roles (user_id, role, created_at, updated_at)
        VALUES (new_user_id, 'worker', NOW(), NOW())
        ON CONFLICT (user_id) DO UPDATE SET
            role = EXCLUDED.role,
            updated_at = NOW();
    END IF;
    
    -- تسجيل النشاط
    RAISE NOTICE 'تم إنشاء عامل جديد بنجاح: % (%) - ID: %', p_full_name, p_email, new_user_id;
    
    RETURN new_user_id;
    
EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'خطأ في إنشاء العامل: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- وظيفة للحصول على بيانات العامل الكاملة
-- ========================================

CREATE OR REPLACE FUNCTION get_worker_details(worker_user_id UUID)
RETURNS TABLE(
    user_id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    phone VARCHAR(20),
    role VARCHAR(20),
    is_active BOOLEAN,
    worker_id UUID,
    specialty VARCHAR(100),
    experience_years INTEGER,
    hourly_rate DECIMAL(10,2),
    performance_rating DECIMAL(3,2),
    total_completed_orders INTEGER,
    skills TEXT[],
    bio TEXT,
    is_available BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        u.full_name,
        u.phone,
        u.role,
        u.is_active,
        w.id as worker_id,
        w.specialty,
        w.experience_years,
        w.hourly_rate,
        w.performance_rating,
        w.total_completed_orders,
        w.skills,
        w.bio,
        w.is_available,
        u.created_at
    FROM public.users u
    JOIN public.workers w ON u.id = w.user_id
    WHERE u.id = worker_user_id AND u.role = 'worker';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- وظيفة للحصول على جميع العمال
-- ========================================

CREATE OR REPLACE FUNCTION get_all_workers()
RETURNS TABLE(
    user_id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    phone VARCHAR(20),
    specialty VARCHAR(100),
    experience_years INTEGER,
    hourly_rate DECIMAL(10,2),
    performance_rating DECIMAL(3,2),
    total_completed_orders INTEGER,
    skills TEXT[],
    bio TEXT,
    is_available BOOLEAN,
    is_active BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- التحقق من الصلاحيات
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'يجب تسجيل الدخول لعرض العمال';
    END IF;
    
    -- المشرفون يمكنهم رؤية جميع العمال
    IF EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
        RETURN QUERY
        SELECT 
            u.id as user_id,
            u.email,
            u.full_name,
            u.phone,
            w.specialty,
            w.experience_years,
            w.hourly_rate,
            w.performance_rating,
            w.total_completed_orders,
            w.skills,
            w.bio,
            w.is_available,
            u.is_active,
            u.created_at
        FROM public.users u
        JOIN public.workers w ON u.id = w.user_id
        WHERE u.role = 'worker'
        ORDER BY w.performance_rating DESC, u.created_at DESC;
    ELSE
        -- العملاء يمكنهم رؤية العمال المتاحين فقط
        RETURN QUERY
        SELECT 
            u.id as user_id,
            u.email,
            u.full_name,
            u.phone,
            w.specialty,
            w.experience_years,
            w.hourly_rate,
            w.performance_rating,
            w.total_completed_orders,
            w.skills,
            w.bio,
            w.is_available,
            u.is_active,
            u.created_at
        FROM public.users u
        JOIN public.workers w ON u.id = w.user_id
        WHERE u.role = 'worker' 
        AND u.is_active = true 
        AND w.is_available = true
        ORDER BY w.performance_rating DESC;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- منح الصلاحيات للوظائف
-- ========================================

SELECT '🔐 منح الصلاحيات للوظائف' as grant_permissions_title;

-- السماح للمستخدمين المصادق عليهم باستخدام الوظائف
GRANT EXECUTE ON FUNCTION create_worker(
    VARCHAR(255), VARCHAR(255), VARCHAR(100), VARCHAR(20),
    INTEGER, DECIMAL(10,2), TEXT[], TEXT, BOOLEAN, BOOLEAN
) TO authenticated;

GRANT EXECUTE ON FUNCTION get_worker_details(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_workers() TO authenticated;

-- ========================================
-- التحقق من إنشاء الوظائف
-- ========================================

SELECT '✅ التحقق من إنشاء الوظائف' as verification_title;

-- فحص وجود الوظائف
SELECT 
    routine_name as function_name,
    '✅ موجودة' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('create_worker', 'get_worker_details', 'get_all_workers')
ORDER BY routine_name;

-- عد الوظائف المُنشأة
SELECT 
    'إجمالي الوظائف المُنشأة:' as summary,
    COUNT(*) as functions_created
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('create_worker', 'get_worker_details', 'get_all_workers');

-- ========================================
-- اختبار سريع للوظائف
-- ========================================

SELECT '🧪 اختبار سريع للوظائف' as quick_test_title;

-- اختبار وظيفة الحصول على العمال (يجب أن تعمل حتى لو لم يكن هناك عمال)
DO $$
DECLARE
    workers_count INTEGER;
    error_message TEXT;
BEGIN
    BEGIN
        SELECT COUNT(*) INTO workers_count FROM get_all_workers();
        RAISE NOTICE '✅ وظيفة get_all_workers تعمل بشكل صحيح - العمال الحاليين: %', workers_count;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
        RAISE NOTICE '⚠️ تحذير في وظيفة get_all_workers: %', error_message;
    END;
END $$;

SELECT '🎉 تم إنشاء وظائف إدارة العمال بنجاح!' as completion_message;
