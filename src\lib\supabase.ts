// Robust Supabase client with fallback handling
import { createClient } from '@supabase/supabase-js'

// Get Supabase configuration from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if we have valid Supabase configuration
const isValidSupabaseConfig =
  supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder.key'

// Create Supabase client with enhanced error handling
export const supabase = (() => {
  if (!isValidSupabaseConfig) {
    console.warn('Supabase configuration invalid or missing');
    return null;
  }

  try {
    return createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      global: {
        headers: {
          'X-Client-Info': 'yasmin-al<PERSON>@1.0.0',
        },
      },
    });
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    return null;
  }
})();

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = () => isValidSupabaseConfig && supabase !== null

// Helper function to get configuration status
export const getSupabaseStatus = () => {
  if (!supabaseUrl || !supabaseAnonKey) {
    return {
      configured: false,
      message: 'متغيرات البيئة مفقودة - Environment variables missing'
    }
  }

  if (!isValidSupabaseConfig) {
    return {
      configured: false,
      message: 'يرجى تحديث متغيرات البيئة بقيم حقيقية - Please update environment variables with real values'
    }
  }

  return {
    configured: true,
    message: 'Supabase مُعد بشكل صحيح - Supabase configured correctly'
  }
}

// Enhanced authentication helpers
export const auth = {
  // Sign in with email and password
  async signIn(email: string, password: string) {
    if (!supabase) {
      return { user: null, error: { message: 'Supabase not configured' } }
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      return { user: data.user, session: data.session, error }
    } catch (err) {
      console.error('Sign in error:', err)
      return { user: null, session: null, error: { message: 'Authentication failed' } }
    }
  },

  // Sign out
  async signOut() {
    if (!supabase) {
      return { error: { message: 'Supabase not configured' } }
    }

    try {
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (err) {
      console.error('Sign out error:', err)
      return { error: { message: 'Sign out failed' } }
    }
  },

  // Get current session
  async getSession() {
    if (!supabase) {
      return { session: null, error: { message: 'Supabase not configured' } }
    }

    try {
      const { data, error } = await supabase.auth.getSession()
      return { session: data.session, error }
    } catch (err) {
      console.error('Get session error:', err)
      return { session: null, error: { message: 'Failed to get session' } }
    }
  },

  // Get current user
  async getUser() {
    if (!supabase) {
      return { user: null, error: { message: 'Supabase not configured' } }
    }

    try {
      const { data, error } = await supabase.auth.getUser()
      return { user: data.user, error }
    } catch (err) {
      console.error('Get user error:', err)
      return { user: null, error: { message: 'Failed to get user' } }
    }
  },

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    if (!supabase) {
      console.warn('Supabase not configured for auth state changes')
      return { data: { subscription: null } }
    }

    return supabase.auth.onAuthStateChange(callback)
  }
}
