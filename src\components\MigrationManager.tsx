// Migration Manager Component
// مكون إدارة الترحيل

'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react'
import { storeMigration, isMigrationNeeded, type MigrationStatus } from '@/lib/migration-utility'
import { isSupabaseConfigured } from '@/lib/supabase'

interface MigrationManagerProps {
  onMigrationComplete?: () => void
  autoStart?: boolean
}

export const MigrationManager: React.FC<MigrationManagerProps> = ({
  onMigrationComplete,
  autoStart = false
}) => {
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [needsMigration, setNeedsMigration] = useState(false)

  useEffect(() => {
    // Check if migration is needed
    const checkMigration = () => {
      const needed = isMigrationNeeded()
      setNeedsMigration(needed)
      
      if (needed && autoStart) {
        handleRunMigration()
      }
    }
    
    checkMigration()
  }, [autoStart])

  const handleRunMigration = async () => {
    setIsRunning(true)
    
    try {
      const status = await storeMigration.runFullMigration()
      setMigrationStatus(status)
      
      if (status.isComplete && onMigrationComplete) {
        onMigrationComplete()
      }
    } catch (error) {
      console.error('Migration failed:', error)
      setMigrationStatus({
        isComplete: false,
        authMigrated: false,
        ordersMigrated: false,
        workersMigrated: false,
        shopMigrated: false,
        errors: ['Migration failed: ' + (error as Error).message],
        warnings: []
      })
    } finally {
      setIsRunning(false)
    }
  }

  const handleCleanupOldData = () => {
    if (confirm('هل أنت متأكد من حذف البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      storeMigration.cleanupOldData()
      setNeedsMigration(false)
      alert('تم حذف البيانات القديمة بنجاح')
    }
  }

  const getProgressValue = () => {
    if (!migrationStatus) return 0
    
    const completed = [
      migrationStatus.authMigrated,
      migrationStatus.ordersMigrated,
      migrationStatus.workersMigrated,
      migrationStatus.shopMigrated
    ].filter(Boolean).length
    
    return (completed / 4) * 100
  }

  const StatusIcon = ({ success }: { success: boolean }) => (
    success ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    )
  )

  if (!needsMigration && !migrationStatus) {
    return (
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          لا توجد بيانات تحتاج إلى ترحيل - No migration needed
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className={`h-5 w-5 ${isRunning ? 'animate-spin' : ''}`} />
          ترحيل البيانات - Data Migration
        </CardTitle>
        <CardDescription>
          ترحيل البيانات من التخزين المحلي إلى قاعدة البيانات
          <br />
          Migrating data from localStorage to Supabase database
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Supabase Status */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            حالة Supabase: {isSupabaseConfigured() ? 'متصل' : 'غير متصل'} - 
            Supabase Status: {isSupabaseConfigured() ? 'Connected' : 'Not Connected'}
          </AlertDescription>
        </Alert>

        {/* Migration Progress */}
        {migrationStatus && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>تقدم الترحيل - Migration Progress</span>
              <span>{Math.round(getProgressValue())}%</span>
            </div>
            <Progress value={getProgressValue()} className="w-full" />
          </div>
        )}

        {/* Migration Status Details */}
        {migrationStatus && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span>تفاصيل الترحيل - Migration Details</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? 'إخفاء' : 'عرض'} التفاصيل
              </Button>
            </div>
            
            {showDetails && (
              <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <StatusIcon success={migrationStatus.authMigrated} />
                  <span>ترحيل بيانات المصادقة - Authentication Data</span>
                </div>
                <div className="flex items-center gap-2">
                  <StatusIcon success={migrationStatus.ordersMigrated} />
                  <span>ترحيل بيانات الطلبات - Orders Data</span>
                </div>
                <div className="flex items-center gap-2">
                  <StatusIcon success={migrationStatus.workersMigrated} />
                  <span>ترحيل بيانات العمال - Workers Data</span>
                </div>
                <div className="flex items-center gap-2">
                  <StatusIcon success={migrationStatus.shopMigrated} />
                  <span>ترحيل بيانات المتجر - Shop Data</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Errors and Warnings */}
        {migrationStatus?.errors && migrationStatus.errors.length > 0 && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="font-semibold">أخطاء الترحيل - Migration Errors:</div>
              <ul className="list-disc list-inside mt-1">
                {migrationStatus.errors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {migrationStatus?.warnings && migrationStatus.warnings.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="font-semibold">تحذيرات الترحيل - Migration Warnings:</div>
              <ul className="list-disc list-inside mt-1">
                {migrationStatus.warnings.map((warning, index) => (
                  <li key={index} className="text-sm">{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-4">
          <Button
            onClick={handleRunMigration}
            disabled={isRunning}
            className="flex-1"
          >
            {isRunning ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                جاري الترحيل...
              </>
            ) : (
              'بدء الترحيل - Start Migration'
            )}
          </Button>
          
          {migrationStatus?.isComplete && (
            <Button
              variant="outline"
              onClick={handleCleanupOldData}
              className="flex-1"
            >
              حذف البيانات القديمة - Cleanup Old Data
            </Button>
          )}
        </div>

        {/* Success Message */}
        {migrationStatus?.isComplete && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              تم ترحيل البيانات بنجاح! يمكنك الآن استخدام النظام الجديد.
              <br />
              Migration completed successfully! You can now use the new system.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

export default MigrationManager
