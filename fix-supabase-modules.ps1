# Yasmin <PERSON> - Supabase Module Resolution Fix Script
# سكريبت إصلاح مشاكل وحدات Supabase

Write-Host "🔧 Yasmin Alsham - Supabase Module Resolution Fix" -ForegroundColor Cyan
Write-Host "إصلاح مشاكل وحدات Supabase" -ForegroundColor Cyan
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Node.js and npm
Write-Host "Checking Node.js and npm..." -ForegroundColor Yellow
if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js not found! Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    pause
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "❌ npm not found! Please install npm" -ForegroundColor Red
    pause
    exit 1
}

$nodeVersion = node --version
$npmVersion = npm --version
Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
Write-Host ""

# Step 1: Stop any running processes
Write-Host "Step 1: Stopping any running Next.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*next*" } | Stop-Process -Force -ErrorAction SilentlyContinue
Write-Host "✅ Processes stopped" -ForegroundColor Green
Write-Host ""

# Step 2: Clean all cache and build artifacts
Write-Host "Step 2: Cleaning cache and build artifacts..." -ForegroundColor Yellow
$foldersToClean = @(".next", ".turbo", "out", "dist", "build")

foreach ($folder in $foldersToClean) {
    if (Test-Path $folder) {
        Write-Host "Removing $folder..." -ForegroundColor Gray
        Remove-Item -Recurse -Force $folder -ErrorAction SilentlyContinue
    }
}

# Clean npm cache
Write-Host "Cleaning npm cache..." -ForegroundColor Gray
npm cache clean --force 2>$null

Write-Host "✅ Cache cleaned" -ForegroundColor Green
Write-Host ""

# Step 3: Verify and reinstall Supabase dependencies
Write-Host "Step 3: Verifying Supabase dependencies..." -ForegroundColor Yellow

# Check if @supabase/supabase-js exists
$supabasePath = "node_modules/@supabase/supabase-js"
if (-not (Test-Path $supabasePath)) {
    Write-Host "❌ @supabase/supabase-js not found, reinstalling..." -ForegroundColor Red
    npm install @supabase/supabase-js@^2.50.0
} else {
    Write-Host "✅ @supabase/supabase-js found" -ForegroundColor Green
}

# Verify package integrity
Write-Host "Verifying package integrity..." -ForegroundColor Gray
npm ls @supabase/supabase-js 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Package integrity issues detected, reinstalling..." -ForegroundColor Yellow
    npm install @supabase/supabase-js@^2.50.0 --force
}

Write-Host "✅ Dependencies verified" -ForegroundColor Green
Write-Host ""

# Step 4: Check for path issues (Arabic characters)
Write-Host "Step 4: Checking for path issues..." -ForegroundColor Yellow
$currentPath = Get-Location
if ($currentPath.Path -match '[^\x00-\x7F]') {
    Write-Host "⚠️ Warning: Path contains non-ASCII characters which may cause module resolution issues" -ForegroundColor Yellow
    Write-Host "Current path: $currentPath" -ForegroundColor Gray
    Write-Host "Consider moving the project to a path with only ASCII characters" -ForegroundColor Yellow
} else {
    Write-Host "✅ Path is ASCII-compatible" -ForegroundColor Green
}
Write-Host ""

# Step 5: Test module resolution
Write-Host "Step 5: Testing module resolution..." -ForegroundColor Yellow
$testScript = @"
try {
  const { createClient } = require('@supabase/supabase-js');
  console.log('✅ Supabase module loaded successfully');
  console.log('Version:', require('@supabase/supabase-js/package.json').version);
} catch (error) {
  console.error('❌ Module resolution failed:', error.message);
  process.exit(1);
}
"@

$testScript | Out-File -FilePath "temp-test.js" -Encoding UTF8
node temp-test.js
$testResult = $LASTEXITCODE
Remove-Item "temp-test.js" -ErrorAction SilentlyContinue

if ($testResult -eq 0) {
    Write-Host "✅ Module resolution test passed" -ForegroundColor Green
} else {
    Write-Host "❌ Module resolution test failed" -ForegroundColor Red
}
Write-Host ""

# Step 6: Start development server
Write-Host "Step 6: Starting development server..." -ForegroundColor Yellow
Write-Host "Choose an option:" -ForegroundColor Cyan
Write-Host "1. Start with Turbopack (faster, experimental)" -ForegroundColor White
Write-Host "2. Start without Turbopack (stable)" -ForegroundColor White
Write-Host "3. Exit without starting" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "Starting with Turbopack..." -ForegroundColor Green
        npm run dev
    }
    "2" {
        Write-Host "Starting without Turbopack..." -ForegroundColor Green
        npm run dev:safe
    }
    "3" {
        Write-Host "Exiting..." -ForegroundColor Yellow
        exit 0
    }
    default {
        Write-Host "Invalid choice, starting with stable mode..." -ForegroundColor Yellow
        npm run dev:safe
    }
}
