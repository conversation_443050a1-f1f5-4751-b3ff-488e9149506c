-- ياسمين الشام - إصلاح سياسات RLS للمواعيد لدعم الحجز المجهول
-- <PERSON><PERSON><PERSON> - RLS Policy Fix for Anonymous Appointment Booking
-- تاريخ الإنشاء: 2025-01-11

-- ========================================
-- إصلاح جدول المواعيد لدعم المستخدمين المجهولين
-- ========================================

-- أولاً: إزالة السياسات الحالية للمواعيد
DROP POLICY IF EXISTS "Clients can view their own appointments" ON public.appointments;
DROP POLICY IF EXISTS "Clients can create appointments" ON public.appointments;
DROP POLICY IF EXISTS "Clients can update their own appointments" ON public.appointments;
DROP POLICY IF EXISTS "Workers can view their appointments" ON public.appointments;
DROP POLICY IF EXISTS "Workers can update their appointments" ON public.appointments;
DROP POLICY IF EXISTS "Admins can manage all appointments" ON public.appointments;

-- ثانياً: تعديل جدول المواعيد لدعم المستخدمين المجهولين
-- إضافة حقول للمستخدمين المجهولين وجعل client_id اختياري
ALTER TABLE public.appointments 
  ALTER COLUMN client_id DROP NOT NULL,
  ADD COLUMN IF NOT EXISTS guest_name VARCHAR(255),
  ADD COLUMN IF NOT EXISTS guest_phone VARCHAR(20),
  ADD COLUMN IF NOT EXISTS guest_email VARCHAR(255),
  ADD COLUMN IF NOT EXISTS is_guest_booking BOOLEAN DEFAULT false;

-- إضافة قيد للتأكد من وجود إما client_id أو بيانات الضيف
ALTER TABLE public.appointments 
  ADD CONSTRAINT check_client_or_guest 
  CHECK (
    (client_id IS NOT NULL) OR 
    (is_guest_booking = true AND guest_name IS NOT NULL AND guest_phone IS NOT NULL)
  );

-- ثالثاً: إنشاء سياسات RLS جديدة تدعم المستخدمين المجهولين

-- 1. سياسة القراءة - المستخدمون المسجلون يمكنهم رؤية مواعيدهم فقط
CREATE POLICY "Authenticated users can view their own appointments" 
ON public.appointments FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND client_id = auth.uid()
);

-- 2. سياسة القراءة - العمال يمكنهم رؤية مواعيدهم المخصصة
CREATE POLICY "Workers can view their assigned appointments" 
ON public.appointments FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.workers 
    WHERE id = appointments.worker_id AND user_id = auth.uid()
  )
);

-- 3. سياسة القراءة - المشرفون يمكنهم رؤية جميع المواعيد
CREATE POLICY "Admins can view all appointments" 
ON public.appointments FOR SELECT 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- 4. سياسة الإنشاء - المستخدمون المسجلون يمكنهم إنشاء مواعيد لأنفسهم
CREATE POLICY "Authenticated users can create their own appointments" 
ON public.appointments FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL AND 
  client_id = auth.uid() AND 
  is_guest_booking = false
);

-- 5. سياسة الإنشاء - السماح بالحجز المجهول (الأهم!)
CREATE POLICY "Allow anonymous appointment booking" 
ON public.appointments FOR INSERT 
WITH CHECK (
  is_guest_booking = true AND
  guest_name IS NOT NULL AND 
  guest_phone IS NOT NULL AND
  client_id IS NULL
);

-- 6. سياسة التحديث - المستخدمون المسجلون يمكنهم تحديث مواعيدهم
CREATE POLICY "Authenticated users can update their own appointments" 
ON public.appointments FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND client_id = auth.uid()
);

-- 7. سياسة التحديث - العمال يمكنهم تحديث مواعيدهم المخصصة
CREATE POLICY "Workers can update their assigned appointments" 
ON public.appointments FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.workers 
    WHERE id = appointments.worker_id AND user_id = auth.uid()
  )
);

-- 8. سياسة التحديث - المشرفون يمكنهم تحديث جميع المواعيد
CREATE POLICY "Admins can update all appointments" 
ON public.appointments FOR UPDATE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- 9. سياسة الحذف - المشرفون فقط يمكنهم حذف المواعيد
CREATE POLICY "Admins can delete appointments" 
ON public.appointments FOR DELETE 
USING (
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- ========================================
-- إنشاء فهارس إضافية لتحسين الأداء
-- ========================================

-- فهرس للمواعيد المجهولة
CREATE INDEX IF NOT EXISTS idx_appointments_guest_booking 
ON public.appointments(is_guest_booking) 
WHERE is_guest_booking = true;

-- فهرس لرقم هاتف الضيف للبحث السريع
CREATE INDEX IF NOT EXISTS idx_appointments_guest_phone 
ON public.appointments(guest_phone) 
WHERE is_guest_booking = true;

-- فهرس مركب للتاريخ والوقت والحالة
CREATE INDEX IF NOT EXISTS idx_appointments_date_time_status 
ON public.appointments(appointment_date, appointment_time, status);

-- ========================================
-- وظائف مساعدة للمواعيد المجهولة
-- ========================================

-- وظيفة للبحث عن مواعيد الضيوف برقم الهاتف
CREATE OR REPLACE FUNCTION get_guest_appointments(phone_number TEXT)
RETURNS TABLE (
  id UUID,
  guest_name VARCHAR(255),
  guest_phone VARCHAR(20),
  appointment_date DATE,
  appointment_time TIME,
  status VARCHAR(20),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) 
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.guest_name,
    a.guest_phone,
    a.appointment_date,
    a.appointment_time,
    a.status,
    a.notes,
    a.created_at
  FROM public.appointments a
  WHERE a.is_guest_booking = true 
    AND a.guest_phone = phone_number
  ORDER BY a.appointment_date DESC, a.appointment_time DESC;
END;
$$ LANGUAGE plpgsql;

-- وظيفة لإنشاء موعد مجهول بشكل آمن
CREATE OR REPLACE FUNCTION create_guest_appointment(
  p_guest_name VARCHAR(255),
  p_guest_phone VARCHAR(20),
  p_appointment_date DATE,
  p_appointment_time TIME,
  p_guest_email VARCHAR(255) DEFAULT NULL,
  p_service_type VARCHAR(100) DEFAULT 'consultation',
  p_notes TEXT DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  new_appointment_id UUID;
  slot_available BOOLEAN;
BEGIN
  -- التحقق من توفر الموعد
  SELECT NOT EXISTS (
    SELECT 1 FROM public.appointments 
    WHERE appointment_date = p_appointment_date 
      AND appointment_time = p_appointment_time
      AND status NOT IN ('cancelled', 'no_show')
  ) INTO slot_available;
  
  IF NOT slot_available THEN
    RAISE EXCEPTION 'الموعد المحدد غير متاح';
  END IF;
  
  -- إنشاء الموعد
  INSERT INTO public.appointments (
    guest_name,
    guest_phone,
    guest_email,
    appointment_date,
    appointment_time,
    service_type,
    notes,
    is_guest_booking,
    type,
    status
  ) VALUES (
    p_guest_name,
    p_guest_phone,
    p_guest_email,
    p_appointment_date,
    p_appointment_time,
    p_service_type,
    p_notes,
    true,
    'consultation',
    'scheduled'
  ) RETURNING id INTO new_appointment_id;
  
  RETURN new_appointment_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- إدخال بيانات تجريبية للمواعيد المجهولة
-- ========================================

-- إضافة بعض المواعيد المجهولة التجريبية
INSERT INTO public.appointments (
  guest_name,
  guest_phone,
  guest_email,
  appointment_date,
  appointment_time,
  service_type,
  notes,
  is_guest_booking,
  type,
  status
) VALUES 
(
  'ليلى أحمد',
  '+963-987-111111',
  '<EMAIL>',
  CURRENT_DATE + INTERVAL '2 days',
  '17:00',
  'استشارة تصميم فستان زفاف',
  'أريد فستان زفاف كلاسيكي باللون الأبيض',
  true,
  'consultation',
  'scheduled'
),
(
  'نور محمد',
  '+963-987-222222',
  NULL,
  CURRENT_DATE + INTERVAL '4 days',
  '19:00',
  'استشارة فستان سهرة',
  'فستان سهرة للحفلة القادمة',
  true,
  'consultation',
  'scheduled'
);

-- ========================================
-- منح الصلاحيات للوظائف العامة
-- ========================================

-- السماح للجميع باستخدام وظيفة البحث عن المواعيد
GRANT EXECUTE ON FUNCTION get_guest_appointments(TEXT) TO anon, authenticated;

-- السماح للجميع باستخدام وظيفة إنشاء المواعيد المجهولة
GRANT EXECUTE ON FUNCTION create_guest_appointment(
  VARCHAR(255), VARCHAR(20), DATE, TIME, VARCHAR(255), VARCHAR(100), TEXT
) TO anon, authenticated;

-- ========================================
-- رسائل النجاح
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إصلاح سياسات RLS للمواعيد بنجاح!';
    RAISE NOTICE '🎯 الآن يمكن للمستخدمين المجهولين حجز المواعيد';
    RAISE NOTICE '🔒 تم الحفاظ على الأمان للمستخدمين المسجلين';
    RAISE NOTICE '⚡ تم إضافة فهارس لتحسين الأداء';
    RAISE NOTICE '🛠️ تم إضافة وظائف مساعدة للمواعيد المجهولة';
    RAISE NOTICE '📊 تم إدخال بيانات تجريبية للاختبار';
END $$;
