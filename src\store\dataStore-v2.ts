// Enhanced Data Store with Supabase Integration
// متجر البيانات المحسن مع تكامل Supabase

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { isSupabaseConfigured } from '@/lib/supabase'
import { 
  orderService, 
  workerService, 
  appointmentService,
  type Order,
  type Worker,
  type Appointment
} from '@/lib/database-v2'

// Enhanced interfaces for the store
export interface StoreOrder extends Order {
  // Additional client-side properties if needed
  localId?: string
  isDirty?: boolean
}

export interface StoreWorker extends Worker {
  // Additional client-side properties if needed
  localId?: string
  isDirty?: boolean
}

export interface StoreAppointment extends Appointment {
  // Additional client-side properties if needed
  localId?: string
  isDirty?: boolean
}

interface DataState {
  // Data
  orders: StoreOrder[]
  workers: StoreWorker[]
  appointments: StoreAppointment[]
  
  // Loading states
  isLoading: boolean
  isOrdersLoading: boolean
  isWorkersLoading: boolean
  isAppointmentsLoading: boolean
  
  // Error states
  error: string | null
  ordersError: string | null
  workersError: string | null
  appointmentsError: string | null

  // Order management
  loadOrders: (filters?: { status?: string; clientId?: string; workerId?: string }) => Promise<void>
  addOrder: (order: Omit<Order, 'id' | 'created_at' | 'updated_at'>) => Promise<string | null>
  updateOrder: (id: string, updates: Partial<Order>) => Promise<boolean>
  deleteOrder: (id: string) => Promise<boolean>
  getOrder: (id: string) => StoreOrder | undefined

  // Worker management
  loadWorkers: () => Promise<void>
  addWorker: (worker: Omit<Worker, 'id' | 'created_at' | 'updated_at'>) => Promise<string | null>
  updateWorker: (id: string, updates: Partial<Worker>) => Promise<boolean>
  deleteWorker: (id: string) => Promise<boolean>
  getWorker: (id: string) => StoreWorker | undefined

  // Appointment management (for admin/worker views)
  loadAppointments: (filters?: { clientId?: string; workerId?: string; status?: string }) => Promise<void>
  updateAppointment: (id: string, updates: Partial<Appointment>) => Promise<boolean>
  getAppointment: (id: string) => StoreAppointment | undefined

  // Utility functions
  clearError: () => void
  clearAllErrors: () => void
  refreshAll: () => Promise<void>
  
  // Statistics
  getStats: () => {
    totalOrders: number
    totalWorkers: number
    totalAppointments: number
    activeOrders: number
    completedOrders: number
    totalRevenue: number
  }
}

// Generate unique ID for fallback mode
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Mock data for fallback mode
const mockOrders: StoreOrder[] = []
const mockWorkers: StoreWorker[] = []
const mockAppointments: StoreAppointment[] = []

export const useDataStore = create<DataState>()(
  persist(
    (set, get) => ({
      // Initial state
      orders: [],
      workers: [],
      appointments: [],
      isLoading: false,
      isOrdersLoading: false,
      isWorkersLoading: false,
      isAppointmentsLoading: false,
      error: null,
      ordersError: null,
      workersError: null,
      appointmentsError: null,

      // Order management
      loadOrders: async (filters) => {
        set({ isOrdersLoading: true, ordersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { orders, error } = await orderService.getAllOrders(filters)
            
            if (error) {
              set({ ordersError: error.message, isOrdersLoading: false })
              return
            }
            
            set({ orders: orders || [], isOrdersLoading: false })
          } else {
            // Fallback to mock data
            await new Promise(resolve => setTimeout(resolve, 500))
            let filteredOrders = [...mockOrders]
            
            if (filters?.status) {
              filteredOrders = filteredOrders.filter(o => o.status === filters.status)
            }
            if (filters?.clientId) {
              filteredOrders = filteredOrders.filter(o => o.client_id === filters.clientId)
            }
            if (filters?.workerId) {
              filteredOrders = filteredOrders.filter(o => o.worker_id === filters.workerId)
            }
            
            set({ orders: filteredOrders, isOrdersLoading: false })
          }
        } catch (error) {
          console.error('Load orders error:', error)
          set({ ordersError: 'خطأ في تحميل الطلبات', isOrdersLoading: false })
        }
      },

      addOrder: async (orderData) => {
        set({ isOrdersLoading: true, ordersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { order, error } = await orderService.createOrder(orderData)
            
            if (error || !order) {
              set({ ordersError: error?.message || 'خطأ في إضافة الطلب', isOrdersLoading: false })
              return null
            }
            
            // Add to local state
            set(state => ({
              orders: [...state.orders, order],
              isOrdersLoading: false
            }))
            
            return order.id
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 500))
            
            const newOrder: StoreOrder = {
              ...orderData,
              id: generateId(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              localId: generateId()
            }
            
            mockOrders.push(newOrder)
            set(state => ({
              orders: [...state.orders, newOrder],
              isOrdersLoading: false
            }))
            
            return newOrder.id
          }
        } catch (error) {
          console.error('Add order error:', error)
          set({ ordersError: 'خطأ في إضافة الطلب', isOrdersLoading: false })
          return null
        }
      },

      updateOrder: async (id, updates) => {
        set({ isOrdersLoading: true, ordersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { order, error } = await orderService.updateOrder(id, updates)
            
            if (error || !order) {
              set({ ordersError: error?.message || 'خطأ في تحديث الطلب', isOrdersLoading: false })
              return false
            }
            
            // Update local state
            set(state => ({
              orders: state.orders.map(o => o.id === id ? order : o),
              isOrdersLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const orderIndex = mockOrders.findIndex(o => o.id === id)
            if (orderIndex !== -1) {
              mockOrders[orderIndex] = { 
                ...mockOrders[orderIndex], 
                ...updates, 
                updated_at: new Date().toISOString() 
              }
            }
            
            set(state => ({
              orders: state.orders.map(o => 
                o.id === id 
                  ? { ...o, ...updates, updated_at: new Date().toISOString() }
                  : o
              ),
              isOrdersLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Update order error:', error)
          set({ ordersError: 'خطأ في تحديث الطلب', isOrdersLoading: false })
          return false
        }
      },

      deleteOrder: async (id) => {
        set({ isOrdersLoading: true, ordersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { error } = await orderService.deleteOrder(id)
            
            if (error) {
              set({ ordersError: error.message, isOrdersLoading: false })
              return false
            }
            
            // Remove from local state
            set(state => ({
              orders: state.orders.filter(o => o.id !== id),
              isOrdersLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const orderIndex = mockOrders.findIndex(o => o.id === id)
            if (orderIndex !== -1) {
              mockOrders.splice(orderIndex, 1)
            }
            
            set(state => ({
              orders: state.orders.filter(o => o.id !== id),
              isOrdersLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Delete order error:', error)
          set({ ordersError: 'خطأ في حذف الطلب', isOrdersLoading: false })
          return false
        }
      },

      getOrder: (id) => {
        return get().orders.find(order => order.id === id)
      },

      // Worker management
      loadWorkers: async () => {
        set({ isWorkersLoading: true, workersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { workers, error } = await workerService.getAllWorkers()
            
            if (error) {
              set({ workersError: error.message, isWorkersLoading: false })
              return
            }
            
            set({ workers: workers || [], isWorkersLoading: false })
          } else {
            // Fallback to mock data
            await new Promise(resolve => setTimeout(resolve, 500))
            set({ workers: [...mockWorkers], isWorkersLoading: false })
          }
        } catch (error) {
          console.error('Load workers error:', error)
          set({ workersError: 'خطأ في تحميل العمال', isWorkersLoading: false })
        }
      },

      addWorker: async (workerData) => {
        set({ isWorkersLoading: true, workersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { worker, error } = await workerService.createWorker(workerData)
            
            if (error || !worker) {
              set({ workersError: error?.message || 'خطأ في إضافة العامل', isWorkersLoading: false })
              return null
            }
            
            // Add to local state
            set(state => ({
              workers: [...state.workers, worker],
              isWorkersLoading: false
            }))
            
            return worker.id
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 500))
            
            const newWorker: StoreWorker = {
              ...workerData,
              id: generateId(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              localId: generateId()
            }
            
            mockWorkers.push(newWorker)
            set(state => ({
              workers: [...state.workers, newWorker],
              isWorkersLoading: false
            }))
            
            return newWorker.id
          }
        } catch (error) {
          console.error('Add worker error:', error)
          set({ workersError: 'خطأ في إضافة العامل', isWorkersLoading: false })
          return null
        }
      },

      updateWorker: async (id, updates) => {
        set({ isWorkersLoading: true, workersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { worker, error } = await workerService.updateWorker(id, updates)
            
            if (error || !worker) {
              set({ workersError: error?.message || 'خطأ في تحديث العامل', isWorkersLoading: false })
              return false
            }
            
            // Update local state
            set(state => ({
              workers: state.workers.map(w => w.id === id ? worker : w),
              isWorkersLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const workerIndex = mockWorkers.findIndex(w => w.id === id)
            if (workerIndex !== -1) {
              mockWorkers[workerIndex] = { 
                ...mockWorkers[workerIndex], 
                ...updates, 
                updated_at: new Date().toISOString() 
              }
            }
            
            set(state => ({
              workers: state.workers.map(w => 
                w.id === id 
                  ? { ...w, ...updates, updated_at: new Date().toISOString() }
                  : w
              ),
              isWorkersLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Update worker error:', error)
          set({ workersError: 'خطأ في تحديث العامل', isWorkersLoading: false })
          return false
        }
      },

      deleteWorker: async (id) => {
        set({ isWorkersLoading: true, workersError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { error } = await workerService.deleteWorker(id)
            
            if (error) {
              set({ workersError: error.message, isWorkersLoading: false })
              return false
            }
            
            // Remove from local state
            set(state => ({
              workers: state.workers.filter(w => w.id !== id),
              isWorkersLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const workerIndex = mockWorkers.findIndex(w => w.id === id)
            if (workerIndex !== -1) {
              mockWorkers.splice(workerIndex, 1)
            }
            
            set(state => ({
              workers: state.workers.filter(w => w.id !== id),
              isWorkersLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Delete worker error:', error)
          set({ workersError: 'خطأ في حذف العامل', isWorkersLoading: false })
          return false
        }
      },

      getWorker: (id) => {
        return get().workers.find(worker => worker.id === id)
      },

      // Appointment management
      loadAppointments: async (filters) => {
        set({ isAppointmentsLoading: true, appointmentsError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { appointments, error } = await appointmentService.getAllAppointments(filters)
            
            if (error) {
              set({ appointmentsError: error.message, isAppointmentsLoading: false })
              return
            }
            
            set({ appointments: appointments || [], isAppointmentsLoading: false })
          } else {
            // Fallback to mock data
            await new Promise(resolve => setTimeout(resolve, 500))
            let filteredAppointments = [...mockAppointments]
            
            if (filters?.status) {
              filteredAppointments = filteredAppointments.filter(a => a.status === filters.status)
            }
            if (filters?.clientId) {
              filteredAppointments = filteredAppointments.filter(a => a.client_id === filters.clientId)
            }
            if (filters?.workerId) {
              filteredAppointments = filteredAppointments.filter(a => a.worker_id === filters.workerId)
            }
            
            set({ appointments: filteredAppointments, isAppointmentsLoading: false })
          }
        } catch (error) {
          console.error('Load appointments error:', error)
          set({ appointmentsError: 'خطأ في تحميل المواعيد', isAppointmentsLoading: false })
        }
      },

      updateAppointment: async (id, updates) => {
        set({ isAppointmentsLoading: true, appointmentsError: null })
        
        try {
          if (isSupabaseConfigured()) {
            const { appointment, error } = await appointmentService.updateAppointment(id, updates)
            
            if (error || !appointment) {
              set({ appointmentsError: error?.message || 'خطأ في تحديث الموعد', isAppointmentsLoading: false })
              return false
            }
            
            // Update local state
            set(state => ({
              appointments: state.appointments.map(a => a.id === id ? appointment : a),
              isAppointmentsLoading: false
            }))
            
            return true
          } else {
            // Fallback mode
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const appointmentIndex = mockAppointments.findIndex(a => a.id === id)
            if (appointmentIndex !== -1) {
              mockAppointments[appointmentIndex] = { 
                ...mockAppointments[appointmentIndex], 
                ...updates, 
                updated_at: new Date().toISOString() 
              }
            }
            
            set(state => ({
              appointments: state.appointments.map(a => 
                a.id === id 
                  ? { ...a, ...updates, updated_at: new Date().toISOString() }
                  : a
              ),
              isAppointmentsLoading: false
            }))
            
            return true
          }
        } catch (error) {
          console.error('Update appointment error:', error)
          set({ appointmentsError: 'خطأ في تحديث الموعد', isAppointmentsLoading: false })
          return false
        }
      },

      getAppointment: (id) => {
        return get().appointments.find(appointment => appointment.id === id)
      },

      // Utility functions
      clearError: () => {
        set({ error: null })
      },

      clearAllErrors: () => {
        set({ 
          error: null, 
          ordersError: null, 
          workersError: null, 
          appointmentsError: null 
        })
      },

      refreshAll: async () => {
        const state = get()
        await Promise.all([
          state.loadOrders(),
          state.loadWorkers(),
          state.loadAppointments()
        ])
      },

      // Statistics
      getStats: () => {
        const state = get()
        return {
          totalOrders: state.orders.length,
          totalWorkers: state.workers.length,
          totalAppointments: state.appointments.length,
          activeOrders: state.orders.filter(o => ['pending', 'in_progress'].includes(o.status)).length,
          completedOrders: state.orders.filter(o => o.status === 'completed').length,
          totalRevenue: state.orders
            .filter(o => o.status === 'completed')
            .reduce((sum, order) => sum + (order.total_price || 0), 0)
        }
      }
    }),
    {
      name: 'yasmin-data-storage-v2',
      partialize: (state) => ({
        // Only persist data when in fallback mode
        orders: isSupabaseConfigured() ? [] : state.orders,
        workers: isSupabaseConfigured() ? [] : state.workers,
        appointments: isSupabaseConfigured() ? [] : state.appointments
      })
    }
  )
)
